#!/usr/bin/env python3
"""
Debug script to check environment variable loading.

This script helps debug issues with .env file loading and environment variables.
"""

import os
import sys
from pathlib import Path

def check_env_file():
    """Check for .env file in various locations."""
    current_dir = Path.cwd()
    print(f"Current working directory: {current_dir}")
    
    # Check current directory
    env_file = current_dir / ".env"
    if env_file.exists():
        print(f"✓ Found .env file: {env_file}")
        return env_file
    else:
        print(f"✗ No .env file in current directory: {env_file}")
    
    # Check parent directories
    for i, parent in enumerate(current_dir.parents[:3]):
        env_file = parent / ".env"
        if env_file.exists():
            print(f"✓ Found .env file in parent {i+1}: {env_file}")
            return env_file
        else:
            print(f"✗ No .env file in parent {i+1}: {env_file}")
    
    print("✗ No .env file found in current directory or parents")
    return None

def check_environment_vars():
    """Check relevant environment variables."""
    print("\nEnvironment variables:")
    
    # Check Discord token
    discord_token = os.getenv("DISCORD_BOT_TOKEN")
    if discord_token:
        print(f"✓ DISCORD_BOT_TOKEN: {discord_token[:10]}...{discord_token[-4:]}")
    else:
        print("✗ DISCORD_BOT_TOKEN: Not set")
    
    # Check Google API key
    google_key1 = os.getenv("GOOGLE_AI_API_KEY")
    google_key2 = os.getenv("GOOGLE_API_KEY")
    
    if google_key1:
        print(f"✓ GOOGLE_AI_API_KEY: {google_key1[:10]}...{google_key1[-4:]}")
    else:
        print("✗ GOOGLE_AI_API_KEY: Not set")
    
    if google_key2:
        print(f"✓ GOOGLE_API_KEY: {google_key2[:10]}...{google_key2[-4:]}")
    else:
        print("✗ GOOGLE_API_KEY: Not set")
    
    # Show all environment variables containing relevant keywords
    relevant_vars = [
        k for k in os.environ.keys() 
        if any(keyword in k.upper() for keyword in ['DISCORD', 'TOKEN', 'GOOGLE', 'API'])
    ]
    
    if relevant_vars:
        print(f"\nAll relevant environment variables: {relevant_vars}")
    else:
        print("\nNo relevant environment variables found")

def test_dotenv_loading():
    """Test loading .env file with python-dotenv."""
    try:
        from dotenv import load_dotenv
        
        env_file = check_env_file()
        if env_file:
            print(f"\nTesting dotenv loading from: {env_file}")
            result = load_dotenv(env_file)
            print(f"load_dotenv() result: {result}")
        else:
            print("\nTesting dotenv loading from environment...")
            result = load_dotenv()
            print(f"load_dotenv() result: {result}")
        
        # Check variables after loading
        print("\nAfter dotenv loading:")
        check_environment_vars()
        
    except ImportError:
        print("✗ python-dotenv not installed")

def test_config_loading():
    """Test loading the bot configuration."""
    try:
        sys.path.insert(0, 'src')
        from john.models.config_models import BotConfig
        
        print("\nTesting config loading...")
        config = BotConfig()
        print("✓ Config loaded successfully!")
        print(f"Discord token: {config.discord_token[:10]}...{config.discord_token[-4:]}")
        print(f"Google API key: {config.google_api_key or 'Not set'}")
        
    except Exception as e:
        print(f"✗ Config loading failed: {e}")

def main():
    """Main debug function."""
    print("🔍 John Bot Environment Debug")
    print("=" * 40)
    
    print("\n1. Checking for .env files...")
    check_env_file()
    
    print("\n2. Checking environment variables (before dotenv)...")
    check_environment_vars()
    
    print("\n3. Testing dotenv loading...")
    test_dotenv_loading()
    
    print("\n4. Testing config loading...")
    test_config_loading()
    
    print(f"\n{'=' * 40}")
    print("Debug complete!")

if __name__ == "__main__":
    main()
