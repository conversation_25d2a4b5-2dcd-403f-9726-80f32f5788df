#!/usr/bin/env python3
"""
Migration script to help transition from old to new John bot structure.

This script helps migrate configuration and data from the old bot structure
to the new refactored structure.
"""

import json
import shutil
import sys
from pathlib import Path


def migrate_memory_file():
    """Migrate the memory.json file to the new location if needed."""
    old_memory = Path("memory.json")
    new_memory = Path("memory.json")  # Same location for now
    
    if old_memory.exists():
        print(f"✓ Memory file found at {old_memory}")
        # Memory file is already in the right place
        return True
    else:
        print("ℹ No memory file found - will start with empty memory")
        return False


def backup_old_files():
    """Create a backup of old Python files."""
    old_files = [
        "main.py",
        "bot_service.py", 
        "config.py",
        "llm_interface.py",
        "memory_manager.py",
        "message_cache.py",
        "discord_utils.py"
    ]
    
    backup_dir = Path("old_backup")
    backup_dir.mkdir(exist_ok=True)
    
    backed_up = []
    for file_path in old_files:
        old_file = Path(file_path)
        if old_file.exists():
            backup_file = backup_dir / old_file.name
            shutil.copy2(old_file, backup_file)
            backed_up.append(file_path)
            print(f"✓ Backed up {file_path} to {backup_file}")
    
    if backed_up:
        print(f"\n✓ Backed up {len(backed_up)} files to {backup_dir}/")
        return True
    else:
        print("ℹ No old files found to backup")
        return False


def check_environment():
    """Check if required environment variables are set."""
    import os
    
    required_vars = ["DISCORD_BOT_TOKEN"]
    optional_vars = ["GOOGLE_AI_API_KEY", "GOOGLE_API_KEY"]
    
    missing_required = []
    missing_optional = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
    
    for var in optional_vars:
        if not os.getenv(var):
            missing_optional.append(var)
    
    if missing_required:
        print(f"❌ Missing required environment variables: {', '.join(missing_required)}")
        return False
    else:
        print("✓ Required environment variables are set")
    
    if missing_optional:
        print(f"⚠ Missing optional environment variables: {', '.join(missing_optional)}")
        print("  LLM features will not work without Google API key")
    
    return True


def test_new_structure():
    """Test that the new structure can be imported."""
    try:
        from src.john.config.settings import get_config
        from src.john.core.bot import JohnBot
        
        # Test config loading
        config = get_config()
        print("✓ Configuration loads successfully")
        
        # Test bot creation (don't start it)
        bot = JohnBot()
        print("✓ Bot can be instantiated")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you've installed the package with: pip install -e .")
        return False
    except Exception as e:
        print(f"❌ Error testing new structure: {e}")
        return False


def install_dependencies():
    """Install the new dependencies."""
    import subprocess
    
    try:
        print("Installing dependencies...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-e", "."
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def main():
    """Main migration function."""
    print("🤖 John Discord Bot Migration Script")
    print("=" * 40)
    
    steps = [
        ("Checking environment variables", check_environment),
        ("Installing dependencies", install_dependencies),
        ("Backing up old files", backup_old_files),
        ("Migrating memory file", migrate_memory_file),
        ("Testing new structure", test_new_structure),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            if step_func():
                success_count += 1
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
    
    print(f"\n{'=' * 40}")
    print(f"Migration completed: {success_count}/{len(steps)} steps successful")
    
    if success_count == len(steps):
        print("\n🎉 Migration successful!")
        print("\nTo run the new bot:")
        print("  python -m src.john.main")
        print("\nOr use the old files (backed up in old_backup/) if needed.")
    else:
        print(f"\n⚠ Migration partially completed ({success_count}/{len(steps)} steps)")
        print("Please check the errors above and resolve them.")
        
        if success_count >= 2:  # If dependencies and backup worked
            print("\nYou can still try running the new bot:")
            print("  python -m src.john.main")


if __name__ == "__main__":
    main()
