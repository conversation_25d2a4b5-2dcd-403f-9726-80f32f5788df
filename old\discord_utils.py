import discord
import re
import asyncio
import logging
from typing import Dict, Any, Optional

# Import the config object directly
from config import config as bot_config

logger = logging.getLogger(__name__)


def is_allowed_channel(channel: Optional[discord.abc.GuildChannel]) -> bool:
    """Checks if a channel is configured to be allowed based on server and channel name settings."""
    # Must have at least one restriction defined (server or channel names)
    has_restrictions = bool(
        bot_config.allowed_server_ids or bot_config.allowed_channel_names
    )

    if not has_restrictions:
        logger.warning(
            "No server or channel restrictions defined in config. Allowing all channels by default, which might be unintended."
        )
        return True  # Or False, depending on desired default behavior when no restrictions set

    if not isinstance(channel, discord.TextChannel) or not channel.guild:
        return False  # Not a text channel in a server

    # Check if server ID is in the allowed list
    server_id_match = channel.guild.id in bot_config.allowed_server_ids

    # Check if channel name is in the allowed list (only relevant if server matches or no server restriction)
    channel_name_match = (
        not bot_config.allowed_channel_names  # Allow if channel list is empty
        or channel.name in bot_config.allowed_channel_names
    )

    # Channel is allowed if its server is allowed AND its name is allowed (or name list is empty)
    is_allowed = server_id_match and channel_name_match
    # logger.debug(f"Channel check: #{channel.name} (Server: {channel.guild.id}), Server match: {server_id_match}, Name match: {channel_name_match}, Result: {is_allowed}")
    return is_allowed


def is_allowed_user(user: Optional[discord.User | discord.Member]) -> bool:
    """Checks if a user is the allowed user for commands."""
    if (
        not user or not bot_config.allowed_user_ids
    ):  # Check if allowed_user_ids list exists and is not empty
        return False
    # Check if the user's ID is present in the list
    return user.id in bot_config.allowed_user_ids


async def get_author_info(
    guild: discord.Guild, author: discord.User | discord.Member
) -> Dict[str, Any]:
    """Gets the current display name and username for an author."""
    username = author.name
    display_name = author.name
    member = guild.get_member(author.id)
    if member:
        display_name = member.display_name
    elif isinstance(author, discord.Member):
        display_name = author.display_name

    return {"id": author.id, "username": username, "display_name": display_name}


async def message_to_dict(
    message: discord.Message, guild: discord.Guild
) -> Dict[str, Any]:
    """Converts a discord.Message to a dictionary suitable for the LLM, resolving names."""
    # Note: Spoiler tags are preserved; clean before caching if needed.
    content = message.content or ""  # Use raw content
    author_info_task = get_author_info(guild, message.author)
    mention_info_tasks = [get_author_info(guild, u) for u in message.mentions]
    author_info, *mention_infos = await asyncio.gather(
        author_info_task, *mention_info_tasks
    )

    # Build reaction info with users per emoji
    reactions_info: list[dict[str, Any]] = []
    for r in message.reactions:
        emoji_str = str(r.emoji)
        # Fetch users who reacted with this emoji
        users = [u async for u in r.users()]
        # Resolve user info
        user_info_tasks = [get_author_info(guild, u) for u in users]
        users_info = await asyncio.gather(*user_info_tasks)
        reactions_info.append({"emoji": emoji_str, "users": users_info})
    return {
        "id": str(message.id),
        "author": author_info,
        "content": content,  # Raw content here
        "timestamp": message.created_at.isoformat(),
        "referenced_message": str(message.reference.message_id)
        if message.reference
        else None,
        "mentions": mention_infos,
        "attachments": [att.url for att in message.attachments],
        "reactions": reactions_info,
    }


def clean_message_content(message_content: Optional[str]) -> str:
    """Removes spoilers and trims whitespace. Returns empty string if content is None or only whitespace/spoilers."""
    if not message_content:
        return ""
    content_no_spoilers = re.sub(r"\|\|(.*?)\|\|", "", message_content, flags=re.DOTALL)
    return content_no_spoilers.strip()
