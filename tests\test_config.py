"""
Tests for configuration models and settings.
"""

import pytest
from unittest.mock import patch
import os

from src.john.models.config_models import BotConfig
from src.john.config.settings import get_config


class TestBotConfig:
    """Test cases for BotConfig model."""
    
    def test_config_with_valid_token(self):
        """Test config creation with valid Discord token."""
        with patch.dict(os.environ, {"DISCORD_BOT_TOKEN": "test_token"}):
            config = BotConfig()
            assert config.discord_token == "test_token"
    
    def test_config_missing_token_raises_error(self):
        """Test that missing Discord token raises ValueError."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="DISCORD_BOT_TOKEN environment variable is required"):
                BotConfig()
    
    def test_config_defaults(self):
        """Test that config has expected default values."""
        with patch.dict(os.environ, {"DISCORD_BOT_TOKEN": "test_token"}):
            config = BotConfig()
            
            assert config.allowed_server_ids == [864227641111347221]
            assert "john" in config.allowed_channel_names
            assert config.llm_model_name == "gemini-2.5-flash-preview-05-20"
            assert config.typing_speed_wpm == 150
            assert config.local_timezone == "Europe/Paris"
    
    def test_react_only_channels(self):
        """Test react-only channels configuration."""
        with patch.dict(os.environ, {"DISCORD_BOT_TOKEN": "test_token"}):
            config = BotConfig()
            
            assert "weirdos" in config.react_only_channels
            assert "degenerates" in config.react_only_channels
            assert "talk" in config.react_only_channels
            assert config.max_recent_messages_to_react == 5


class TestConfigSettings:
    """Test cases for config settings module."""
    
    def test_get_config_returns_same_instance(self):
        """Test that get_config returns the same instance (cached)."""
        with patch.dict(os.environ, {"DISCORD_BOT_TOKEN": "test_token"}):
            config1 = get_config()
            config2 = get_config()
            
            assert config1 is config2
    
    def test_get_config_with_google_api_key(self):
        """Test config with Google API key."""
        with patch.dict(os.environ, {
            "DISCORD_BOT_TOKEN": "test_token",
            "GOOGLE_AI_API_KEY": "test_google_key"
        }):
            config = get_config()
            assert config.google_api_key == "test_google_key"
