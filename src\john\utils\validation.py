"""
Validation utilities for the John Discord bot.

This module contains validation functions for various data types
and formats used throughout the application.
"""

import re
import logging

logger = logging.getLogger(__name__)


def validate_emoji(emoji_str: str) -> bool:
    """
    Validate if an emoji string is properly formatted.
    
    Args:
        emoji_str: Emoji string to validate
        
    Returns:
        bool: True if valid emoji format, False otherwise
    """
    if not emoji_str:
        return False
    
    # Check for custom Discord emoji format: <:name:id> or <a:name:id>
    custom_emoji_pattern = r"<a?:\w+:\d+>"
    if re.match(custom_emoji_pattern, emoji_str):
        return True
    
    # For unicode emojis, we'll accept any non-empty string
    # More sophisticated unicode emoji validation could be added here
    return len(emoji_str.strip()) > 0


def validate_message_id(message_id: str | int | None) -> bool:
    """
    Validate if a message ID is properly formatted.
    
    Args:
        message_id: Message ID to validate
        
    Returns:
        bool: True if valid message ID, False otherwise
    """
    if message_id is None:
        return False
    
    try:
        # Convert to int to validate it's a valid Discord snowflake
        id_int = int(message_id)
        # Discord snowflakes are 64-bit integers, so they should be positive
        # and within a reasonable range
        return 0 < id_int < (2**63 - 1)
    except (ValueError, TypeError):
        return False


def validate_channel_name(channel_name: str | None) -> bool:
    """
    Validate if a channel name is properly formatted.
    
    Args:
        channel_name: Channel name to validate
        
    Returns:
        bool: True if valid channel name, False otherwise
    """
    if not channel_name:
        return False
    
    # Discord channel names must be lowercase, no spaces, and contain only
    # alphanumeric characters, hyphens, and underscores
    channel_pattern = r"^[a-z0-9_-]+$"
    return bool(re.match(channel_pattern, channel_name))


def validate_user_id(user_id: str | int | None) -> bool:
    """
    Validate if a user ID is properly formatted.
    
    Args:
        user_id: User ID to validate
        
    Returns:
        bool: True if valid user ID, False otherwise
    """
    if user_id is None:
        return False
    
    try:
        # Convert to int to validate it's a valid Discord snowflake
        id_int = int(user_id)
        # Discord snowflakes are 64-bit integers
        return 0 < id_int < (2**63 - 1)
    except (ValueError, TypeError):
        return False


def validate_content_length(content: str, max_length: int = 2000) -> bool:
    """
    Validate if content length is within Discord limits.
    
    Args:
        content: Content to validate
        max_length: Maximum allowed length (default: 2000 for Discord messages)
        
    Returns:
        bool: True if content length is valid, False otherwise
    """
    if not content:
        return False
    
    return len(content) <= max_length


def sanitize_content(content: str) -> str:
    """
    Sanitize content by removing potentially harmful characters.
    
    Args:
        content: Content to sanitize
        
    Returns:
        str: Sanitized content
    """
    if not content:
        return ""
    
    # Remove null bytes and other control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
    
    # Trim whitespace
    sanitized = sanitized.strip()
    
    return sanitized


def validate_timezone(timezone_str: str) -> bool:
    """
    Validate if a timezone string is valid.
    
    Args:
        timezone_str: Timezone string to validate
        
    Returns:
        bool: True if valid timezone, False otherwise
    """
    if not timezone_str:
        return False
    
    try:
        import pytz
        pytz.timezone(timezone_str)
        return True
    except pytz.exceptions.UnknownTimeZoneError:
        return False
    except Exception:
        return False
