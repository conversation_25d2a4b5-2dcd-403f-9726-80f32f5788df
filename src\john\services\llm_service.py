"""
LLM service for the John Discord bot.

This service handles all interactions with the Large Language Model,
including prompt building, response generation, and error handling.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Any

import pytz
from google import genai
from google.genai import types, errors
from pydantic import ValidationError

from ..models.llm_models import LLMResponse, LLMContext, MemorySnapshot
from ..models.message_models import MessageDict
from ..config.settings import get_config
from ..config.prompts import PromptBuilder

logger = logging.getLogger(__name__)


class LLMService:
    """Service for handling LLM interactions and response generation."""
    
    def __init__(self) -> None:
        """Initialize the LLM service."""
        self.config = get_config()
        self.client: genai.Client | None = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Google GenAI client."""
        if not self.config.google_api_key:
            logger.error(
                "Google AI API Key is missing. LLM functionality cannot be initialized."
            )
            return
        
        try:
            self.client = genai.Client(api_key=self.config.google_api_key)
            logger.info("LLM Service initialized successfully")
        except Exception as e:
            logger.exception(f"Failed to initialize Google GenAI Client: {e}")
            raise RuntimeError(f"Failed to initialize Google GenAI Client: {e}") from e
    
    def _get_time_strings(self) -> tuple[str, str, str]:
        """
        Get current time strings for context.
        
        Returns:
            tuple[str, str, str]: UTC time, local time, timezone name
        """
        utc_now = datetime.now(timezone.utc)
        utc_time_str = utc_now.isoformat()
        local_time_str = "N/A"
        local_timezone_name = "N/A"
        
        if self.config.local_timezone:
            try:
                local_tz = pytz.timezone(self.config.local_timezone)
                local_now = utc_now.astimezone(local_tz)
                local_time_str = local_now.isoformat()
                local_timezone_name = local_now.tzname() or self.config.local_timezone
            except Exception:
                logger.error(f"Error with local timezone {self.config.local_timezone}")
        
        return utc_time_str, local_time_str, local_timezone_name
    
    def _sanitize_message_dict(self, msg_dict: dict[str, Any]) -> dict[str, Any]:
        """
        Replace attachment URLs with placeholders in a message dictionary.
        
        Args:
            msg_dict: Raw message dictionary
            
        Returns:
            dict[str, Any]: Sanitized message dictionary
        """
        sanitized = msg_dict.copy()
        if sanitized.get("attachments"):
            sanitized["attachments"] = ["<attachment>"]
        return sanitized
    
    def _collect_conversation(
        self, 
        messages: list[MessageDict]
    ) -> tuple[list[dict[str, Any]], int]:
        """
        Sanitize and format conversation history within token limit.
        
        Args:
            messages: List of message objects
            
        Returns:
            tuple[list[dict[str, Any]], int]: Processed messages and token estimate
        """
        # Convert to dictionaries and sanitize
        sanitized = [
            self._sanitize_message_dict(msg.to_dict()) 
            for msg in messages
        ]
        
        # Collect messages within token limit
        collected_messages: list[dict[str, Any]] = []
        token_estimate = 0
        
        for msg in reversed(sanitized):
            # Rough token estimation based on JSON length
            msg_json = json.dumps(msg, ensure_ascii=False)
            msg_tokens = len(msg_json.split())
            
            if token_estimate + msg_tokens < self.config.max_tokens - 1000:  # Leave room for prompt
                collected_messages.append(msg)
                token_estimate += msg_tokens
            else:
                break
        
        # Reverse to get chronological order
        collected_messages.reverse()
        
        return collected_messages, token_estimate
    
    def build_context(
        self,
        messages: list[MessageDict],
        memory: MemorySnapshot,
        trigger_type: str,
        available_emojis: list[str],
        channel_name: str,
        is_react_only: bool = False,
    ) -> LLMContext:
        """
        Build LLM context from provided data.
        
        Args:
            messages: List of message objects
            memory: Memory snapshot
            trigger_type: Type of trigger
            available_emojis: Available custom emojis
            channel_name: Discord channel name
            is_react_only: Whether channel is react-only
            
        Returns:
            LLMContext: Complete context for LLM
        """
        utc_time, local_time, tz_name = self._get_time_strings()
        processed_messages, _ = self._collect_conversation(messages)
        
        return LLMContext(
            trigger_type=trigger_type,
            channel_name=channel_name,
            is_react_only=is_react_only,
            available_emojis=available_emojis,
            memory=memory,
            messages=processed_messages,
            current_time=utc_time,
            local_time=local_time,
            local_timezone_name=tz_name,
        )
    
    def build_prompt(self, context: LLMContext) -> str:
        """
        Build the complete prompt for LLM generation.
        
        Args:
            context: LLM context data
            
        Returns:
            str: Complete formatted prompt
        """
        prompt = PromptBuilder.build_full_prompt(context)
        
        # Token estimation and truncation
        token_estimate = len(prompt.split())
        
        logger.info(
            f"Built prompt with {len(context.messages)} messages. "
            f"Token estimate: {token_estimate}. "
            f"Channel type: {'react-only' if context.is_react_only else 'normal'}"
        )
        
        if token_estimate > self.config.max_tokens:
            logger.warning("Prompt exceeds token limit, truncating")
            words = prompt.split()
            truncated_words = words[-self.config.max_tokens:]
            prompt = " ".join(truncated_words)
        
        return prompt
    
    async def _blocking_generate_inner(self, full_prompt: str) -> str:
        """
        Execute the actual blocking call to the Google GenAI API.
        
        Args:
            full_prompt: Complete prompt string
            
        Returns:
            str: Raw response text from LLM
        """
        if not self.client:
            raise RuntimeError("LLM client not initialized")
        
        generation_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=LLMResponse.model_json_schema(),
            thinking_config=types.ThinkingConfig(thinking_budget=24000),
        )
        
        logger.debug("Executing blocking LLM API call")
        response = self.client.models.generate_content(
            model=self.config.llm_model_name,
            contents=full_prompt,
            config=generation_config,
        )
        logger.debug("Blocking LLM API call finished")
        
        if response.text is None:
            logger.error("LLM API returned response with None text content")
            raise ValueError("LLM API returned None response text")
        
        return response.text
    
    async def generate_response(self, context: LLMContext) -> LLMResponse:
        """
        Generate LLM response with timeout and retry handling.
        
        Args:
            context: LLM context data
            
        Returns:
            LLMResponse: Parsed and validated LLM response
        """
        if not self.client:
            logger.error("LLM client not initialized")
            return LLMResponse(
                action="ignore", 
                reason="LLM client not initialized"
            )
        
        full_prompt = self.build_prompt(context)
        llm_start_time = time.monotonic()
        response_text: str | None = None
        max_retries = 2
        timeout_seconds = 20.0
        
        for attempt in range(max_retries):
            logger.info(
                f"Attempting LLM call (Attempt {attempt + 1}/{max_retries}) - "
                f"Timeout: {timeout_seconds}s"
            )
            
            try:
                loop = asyncio.get_running_loop()
                response_text = await asyncio.wait_for(
                    loop.run_in_executor(
                        None, 
                        self._blocking_generate_inner, 
                        full_prompt
                    ),
                    timeout=timeout_seconds,
                )
                logger.info("LLM call successful")
                break
                
            except asyncio.TimeoutError:
                logger.warning(
                    f"LLM call timed out ({timeout_seconds}s) on attempt {attempt + 1}"
                )
                if attempt < max_retries - 1:
                    logger.info("Retrying after 5s delay")
                    await asyncio.sleep(5)
                else:
                    logger.error("LLM call failed due to final timeout after retries")
                    return LLMResponse(
                        action="ignore", 
                        reason="LLM call timed out after retries"
                    )
            
            except errors.ServerError as e:
                logger.warning(f"LLM Server Error on attempt {attempt + 1}: {e}")
                if (
                    "503" in str(e) or "overloaded" in str(e).lower()
                ) and attempt < max_retries - 1:
                    logger.info("Model overloaded. Retrying in 60s")
                    await asyncio.sleep(60)
                else:
                    logger.error(
                        f"LLM call failed: Non-retryable ServerError or "
                        f"max retries reached: {e}"
                    )
                    return LLMResponse(
                        action="ignore", 
                        reason=f"LLM Server Error: {e}"
                    )
            
            except Exception as e:
                logger.exception(
                    f"Unexpected error during LLM call attempt {attempt + 1}"
                )
                return LLMResponse(
                    action="ignore", 
                    reason=f"LLM generation error: {e}"
                )
        
        if response_text is None:
            logger.error(
                "LLM response text is None after retry loop completion"
            )
            return LLMResponse(
                action="ignore", 
                reason="LLM failed after retries (internal state)"
            )
        
        llm_duration = time.monotonic() - llm_start_time
        logger.info(f"LLM call finished in {llm_duration:.2f} seconds")
        logger.debug(f"LLM raw response text: {response_text}")
        
        # Parse and validate response
        try:
            llm_response = LLMResponse.model_validate_json(response_text)
            logger.info(
                f"LLM parsed response: {llm_response.model_dump_json(indent=2)}"
            )
            
            if self.config.test_mode:
                logger.info(
                    f"[TEST MODE] Final LLM Response Decision: "
                    f"{llm_response.model_dump_json(indent=2)}"
                )
            
            return llm_response
            
        except (ValidationError, json.JSONDecodeError, ValueError) as e:
            logger.error(
                f"LLM response validation/parsing error: {e}. "
                f"Raw text: '{response_text}'"
            )
            return LLMResponse(
                action="ignore", 
                reason=f"LLM response parsing error: {e}"
            )
