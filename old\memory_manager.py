import os
import json
import asyncio
import logging
from functools import partial
from typing import Dict, Any, List
from config import config

logger = logging.getLogger(__name__)


class MemoryManager:
    def __init__(self, memory_path: str):
        self.memory_path = memory_path
        self._memory: Dict[str, Any] = {"facts": []}
        self._lock = asyncio.Lock()

    async def load_memory(self) -> None:
        """Loads memory from the JSON file asynchronously."""
        loop = asyncio.get_running_loop()
        if os.path.exists(self.memory_path):
            try:
                with open(self.memory_path, "r", encoding="utf-8") as f:
                    data = await loop.run_in_executor(None, json.load, f)
                async with self._lock:
                    self._memory = {"facts": data.get("facts", [])}
                logger.info(f"Memory loaded successfully from {self.memory_path}")
            except Exception as e:
                logger.error(
                    f"Failed to load memory from {self.memory_path}: {e}. Using default empty memory."
                )
                async with self._lock:
                    self._memory = {"facts": []}
        else:
            logger.info(
                f"Memory file {self.memory_path} not found. Starting with empty memory."
            )
            async with self._lock:
                self._memory = {"facts": []}

    async def save_memory(self) -> None:
        """Saves the current memory state to the JSON file asynchronously."""
        loop = asyncio.get_running_loop()
        try:
            async with self._lock:
                memory_to_save = {"facts": self._memory.get("facts", [])}

            dump_func = partial(json.dump, memory_to_save, indent=2)

            with open(self.memory_path, "w", encoding="utf-8") as f:
                await loop.run_in_executor(None, dump_func, f)
            logger.debug(f"Memory saved successfully to {self.memory_path}")
        except Exception as e:
            logger.error(f"Failed to save memory to {self.memory_path}: {e}")

    async def get_memory_snapshot(self) -> Dict[str, Any]:
        """Returns a copy of the current memory state."""
        async with self._lock:
            return self._memory.copy()

    async def update_memory(self, proposed_facts: List[str]) -> bool:
        """Adds unique new facts to memory and saves if changes were made."""
        if not isinstance(proposed_facts, list) or not proposed_facts:
            return False

        logger.info(
            f"Attempting to update memory with proposed facts: {proposed_facts}"
        )
        updated = False

        async with self._lock:
            if "facts" not in self._memory or not isinstance(
                self._memory.get("facts"), list
            ):
                self._memory["facts"] = []

            current_lower = {
                fact.strip().lower()
                for fact in self._memory["facts"]
                if isinstance(fact, str)
            }
            facts_to_add = []
            skipped_facts = 0
            for fact in proposed_facts:
                if not isinstance(fact, str):
                    logger.warning(
                        f"Skipping non-string item in proposed memory update: {fact}"
                    )
                    skipped_facts += 1
                    continue
                f = fact.strip()
                if f and f.lower() not in current_lower:
                    facts_to_add.append(f)
                    current_lower.add(f.lower())
                    updated = True
                else:
                    skipped_facts += 1

            if facts_to_add:
                self._memory["facts"].extend(facts_to_add)
                logger.info(
                    f"Added {len(facts_to_add)} new facts. Skipped {skipped_facts}."
                )
            else:
                logger.info(f"No new facts to add. Skipped {skipped_facts}.")

        if updated:
            await self.save_memory()

        return updated


memory_manager = MemoryManager(config.memory_path)
