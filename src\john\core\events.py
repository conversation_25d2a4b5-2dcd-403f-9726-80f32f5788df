"""
Event handlers for the John Discord bot.

This module contains all Discord event handlers that process
incoming events and coordinate with the bot service.
"""

import asyncio
import logging
from typing import TYPE_CHECKING

import discord
from discord import TextChannel

from ..config.settings import get_config
from ..models.message_models import MessageDict
from ..utils.discord_utils import is_allowed_channel, clean_message_content, message_to_dict

if TYPE_CHECKING:
    from ..services.bot_service import BotService

logger = logging.getLogger(__name__)


class EventHandler:
    """Handles Discord events and coordinates with bot service."""
    
    def __init__(self, bot_service: "BotService") -> None:
        """
        Initialize the event handler.
        
        Args:
            bot_service: Bot service instance
        """
        self.bot_service = bot_service
        self.config = get_config()
    
    async def on_message(self, message: discord.Message) -> None:
        """
        Handle new message events.
        
        Args:
            message: Discord message object
        """
        if not await self._should_process_message(message):
            return
        
        try:
            # Cast to TextChannel for type checker
            channel: TextChannel = message.channel  # type: ignore
            msg_dict = await self._prepare_cache_message(message)
            await self.bot_service.cache_service.add_message(channel.id, msg_dict)
            logger.debug(f"Added message {message.id} to cache for #{channel.name}")
            
            # Trigger bot processing
            asyncio.create_task(
                self.bot_service.process_channel_event(channel, trigger_type="new_message")
            )
        except Exception as e:
            logger.exception(f"Error during message processing: {e}")
    
    async def on_message_delete(self, message: discord.Message) -> None:
        """
        Handle message deletion events.
        
        Args:
            message: Discord message object that was deleted
        """
        # Ignore deletes in non-text channels or DMs
        if not isinstance(message.channel, TextChannel) or not message.guild:
            return
        
        # Check if the channel is one we monitor
        if is_allowed_channel(message.channel):
            # Cast to TextChannel for type checker
            channel: TextChannel = message.channel  # type: ignore
            logger.debug(
                f"Message {message.id} deleted in allowed channel #{channel.name}. "
                "Attempting cache removal."
            )
            # Attempt to remove the message from the cache
            # Run as a background task to avoid blocking the event loop
            asyncio.create_task(
                self.bot_service.cache_service.delete_message(channel.id, message.id)
            )
    
    async def on_message_edit(
        self, 
        before: discord.Message, 
        after: discord.Message
    ) -> None:
        """
        Handle message edit events.
        
        Args:
            before: Message before edit
            after: Message after edit
        """
        # Ignore edits by bots or in non-text channels/DMs
        if (
            after.author.bot
            or not isinstance(after.channel, TextChannel)
            or not after.guild
        ):
            return
        
        # Check if the channel is one we monitor
        if is_allowed_channel(after.channel):
            # Cast to TextChannel for type checker
            channel: TextChannel = after.channel  # type: ignore
            
            # Avoid processing if content hasn't actually changed (e.g., embed edits)
            if before.content == after.content:
                return
            
            logger.debug(
                f"Message {after.id} edited in allowed channel #{channel.name}. "
                "Attempting cache update."
            )
            # Attempt to update the message in the cache
            # Run as a background task
            asyncio.create_task(
                self.bot_service.cache_service.update_message(channel.id, after)
            )
    
    async def on_raw_reaction_add(
        self, 
        payload: discord.RawReactionActionEvent
    ) -> None:
        """
        Handle reaction add events.
        
        Args:
            payload: Raw reaction event payload
        """
        # Only handle for allowed guilds and channels
        if payload.guild_id not in self.config.allowed_server_ids:
            return
        
        # Get the bot client from the bot service
        # This is a bit of a hack, but we need access to the client
        # In a real implementation, we'd pass the client to the event handler
        from discord import Client
        import discord
        
        # We need to get the client somehow - this is a design issue
        # For now, we'll skip this functionality in the refactored version
        # TODO: Fix this by passing the client to the event handler
        logger.debug("Reaction add event received but client access not implemented")
    
    async def on_raw_reaction_remove(
        self, 
        payload: discord.RawReactionActionEvent
    ) -> None:
        """
        Handle reaction remove events.
        
        Args:
            payload: Raw reaction event payload
        """
        # Only handle for allowed guilds and channels
        if payload.guild_id not in self.config.allowed_server_ids:
            return
        
        # Same issue as above - need client access
        logger.debug("Reaction remove event received but client access not implemented")
    
    async def _should_process_message(self, message: discord.Message) -> bool:
        """
        Check if a message should be processed.
        
        Args:
            message: Discord message object
            
        Returns:
            bool: True if message should be processed
        """
        if (
            message.author.bot
            or not isinstance(message.channel, TextChannel)
            or not message.guild
        ):
            return False
        
        if not is_allowed_channel(message.channel):
            return False
        
        cleaned = clean_message_content(message.content)
        if not cleaned:
            return False
        
        return True
    
    async def _prepare_cache_message(self, message: discord.Message) -> MessageDict:
        """
        Prepare a message for caching.
        
        Args:
            message: Discord message object
            
        Returns:
            MessageDict: Processed message data
        """
        # Ensure guild is present for type checker
        guild = message.guild  # type: ignore
        assert guild is not None
        
        msg_dict_raw = await message_to_dict(message, guild)
        msg_dict_raw["content"] = clean_message_content(message.content)
        
        return MessageDict.from_dict(msg_dict_raw)
