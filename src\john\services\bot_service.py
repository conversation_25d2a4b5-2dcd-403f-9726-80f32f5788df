"""
Main bot service for the John Discord bot.

This service orchestrates all bot functionality including LLM interactions,
memory management, message caching, and Discord actions.
"""

import asyncio
import logging
import random
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Any

import discord

from ..models.llm_models import LLMResponse, LLMAction
from ..models.message_models import MessageDict
from ..config.settings import get_config
from ..utils.discord_utils import (
    is_react_only_channel,
    can_react_to_message,
    message_to_dict,
)
from .llm_service import LLMService
from .memory_service import MemoryService
from .cache_service import CacheService

logger = logging.getLogger(__name__)


class BotService:
    """Main service that orchestrates all bot functionality."""

    def __init__(self) -> None:
        """Initialize the bot service."""
        self.config = get_config()
        self.llm_service = LLMService()
        self.memory_service = MemoryService()
        self.cache_service = CacheService()

        self._processing_locks: dict[int, asyncio.Lock] = {}
        self._self_id: int | None = None
        self._last_check_times: dict[int, float] = {}

    async def initialize(self) -> None:
        """Initialize the bot service and load memory."""
        await self.memory_service.load_memory()
        logger.info("Bot service initialized successfully")

    def set_self_id(self, user_id: int) -> None:
        """
        Store the bot's own user ID after login.

        Args:
            user_id: Discord user ID of the bot
        """
        self._self_id = user_id
        logger.info(f"Bot Service knows Self ID: {self._self_id}")

    async def _get_lock(self, channel_id: int) -> asyncio.Lock:
        """
        Get or create the processing lock for a channel.

        Args:
            channel_id: Discord channel ID

        Returns:
            asyncio.Lock: Lock for the channel
        """
        if channel_id not in self._processing_locks:
            self._processing_locks[channel_id] = asyncio.Lock()
        return self._processing_locks[channel_id]

    def _validate_llm_action(
        self,
        response: LLMResponse,
        history: list[MessageDict],
        channel: discord.TextChannel,
    ) -> LLMResponse:
        """
        Validate the LLM action and enforce channel restrictions.

        Args:
            response: LLM response to validate
            history: Message history
            channel: Discord channel

        Returns:
            LLMResponse: Validated (possibly modified) response
        """
        # Check if this is a react-only channel and prevent replies
        if is_react_only_channel(channel) and response.action == LLMAction.REPLY:
            logger.warning(
                f"LLM attempted to reply in react-only channel #{channel.name}. "
                "Overriding to 'ignore'."
            )
            response.action = LLMAction.IGNORE
            response.message = None
            response.reason += " (Forced ignore: react-only channel)"
            return response

        # Prevent self-replies
        if response.action == LLMAction.REPLY and response.reply_to and self._self_id:
            original_message = next(
                (msg for msg in history if msg.id == response.reply_to), None
            )

            if original_message and original_message.author.id == self._self_id:
                logger.warning(
                    f"LLM attempted to reply to its own message ({response.reply_to}). "
                    "Overriding to 'ignore'."
                )
                response.action = LLMAction.IGNORE
                response.message = None
                response.reason += " (Forced ignore: cannot reply to self)"
                return response

        # Validate reactions in react-only channels are to recent messages
        if (
            is_react_only_channel(channel)
            and response.react_to_message_id
            and response.reaction_emoji
        ):
            target_message = next(
                (msg for msg in history if msg.id == response.react_to_message_id),
                None,
            )
            if target_message and not can_react_to_message(
                target_message.to_dict(), [msg.to_dict() for msg in history]
            ):
                logger.warning(
                    f"LLM attempted to react to old message {response.react_to_message_id} "
                    f"in react-only channel #{channel.name}. Overriding to 'ignore'."
                )
                response.action = LLMAction.IGNORE
                response.react_to_message_id = None
                response.reaction_emoji = None
                response.reason += " (Forced ignore: message too old for reaction)"
                return response

        return response

    async def _get_combined_history(
        self, source_channel: discord.TextChannel
    ) -> list[MessageDict]:
        """
        Fetch history from source channel.

        Args:
            source_channel: Discord text channel

        Returns:
            list[MessageDict]: List of cached messages
        """
        return await self.cache_service.get_history(source_channel.id)

    async def check_channel_inactivity(
        self, source_channel: discord.TextChannel
    ) -> None:
        """
        Check if a source channel has been inactive and trigger LLM if needed.

        Args:
            source_channel: Discord text channel to check
        """
        if not source_channel.guild:
            return

        source_channel_id = source_channel.id

        source_ts = await self.cache_service.get_last_message_timestamp(
            source_channel_id
        )

        if source_ts is None or source_ts == 0.0:
            logger.debug(
                f"Skipping scheduled check for #{source_channel.name}: "
                "No activity recorded."
            )
            return

        last_check_ts = self._last_check_times.get(source_channel_id, 0.0)

        current_time = time.time()
        inactive_threshold = self.config.inactivity_threshold
        time_since_last_message = current_time - source_ts
        time_since_last_check = current_time - last_check_ts

        should_trigger = (time_since_last_message > inactive_threshold) and (
            time_since_last_check > inactive_threshold
        )

        if should_trigger:
            logger.info(
                f"Channel #{source_channel.name} inactive for >{inactive_threshold}s "
                f"(Last msg: {datetime.fromtimestamp(source_ts).isoformat()}, "
                f"Last check: {datetime.fromtimestamp(last_check_ts).isoformat()}). "
                "Triggering scheduled LLM check."
            )
            self._last_check_times[source_channel_id] = current_time

            asyncio.create_task(
                self.process_channel_event(
                    source_channel, trigger_type="scheduled_check"
                )
            )
        else:
            if not (time_since_last_message > inactive_threshold):
                logger.debug(
                    f"Skipping scheduled check for #{source_channel.name}: "
                    f"Activity within last {inactive_threshold}s "
                    f"({time_since_last_message:.0f}s ago)."
                )
            elif not (time_since_last_check > inactive_threshold):
                logger.debug(
                    f"Skipping scheduled check for #{source_channel.name}: "
                    f"Already checked within last {inactive_threshold}s "
                    f"({time_since_last_check:.0f}s ago)."
                )

    async def _prepare_history_and_typing(
        self, channel: discord.TextChannel, trigger_type: str
    ) -> tuple[list[MessageDict] | None, Any | None]:
        """
        Fetch history, handle empty case, and start typing if needed.

        Args:
            channel: Discord text channel
            trigger_type: Type of trigger

        Returns:
            tuple: History list and typing context
        """
        history = await self._get_combined_history(channel)

        if not history and trigger_type == "new_message":
            logger.warning(
                f"Processing new message for #{channel.name} but combined cache "
                "was empty. Retrying fetch."
            )
            history = await self._get_combined_history(channel)
            if not history:
                logger.error(
                    f"Failed to get any history for #{channel.name} after retry."
                )
                return None, None

        typing_ctx = None
        # Don't start typing in react-only channels since we won't be replying
        if trigger_type != "scheduled_check" and not is_react_only_channel(channel):
            typing_ctx = channel.typing()
            await typing_ctx.__aenter__()
            logger.debug(f"Started typing for {trigger_type} in #{channel.name}")

        return history, typing_ctx

    async def _stop_typing_context(self, typing_ctx: Any) -> None:
        """
        Stop typing context if active.

        Args:
            typing_ctx: Discord typing context
        """
        if typing_ctx:
            try:
                await typing_ctx.__aexit__(None, None, None)
            except Exception as e:
                logger.error(f"Error stopping typing context: {e}")

    async def _get_llm_inputs(
        self, channel: discord.TextChannel
    ) -> tuple[Any, list[str]]:
        """
        Retrieve memory snapshot and available custom emojis.

        Args:
            channel: Discord text channel

        Returns:
            tuple: Memory snapshot and emoji list
        """
        memory = await self.memory_service.get_memory_snapshot()
        emojis: list[str] = []

        if channel.guild:
            emojis = [str(emoji) for emoji in channel.guild.emojis if emoji.available]
            logger.debug(
                f"Providing {len(emojis)} available custom emojis to LLM "
                f"for #{channel.name}."
            )
        else:
            logger.warning(
                f"Cannot fetch custom emojis for #{channel.name}: No guild context."
            )

        return memory, emojis

    async def process_channel_event(
        self, channel: discord.TextChannel, trigger_type: str
    ) -> None:
        """
        Process a channel event with LLM and execute appropriate actions.

        Args:
            channel: Discord text channel
            trigger_type: Type of trigger that initiated this processing
        """
        lock = await self._get_lock(channel.id)

        async with lock:
            logger.debug(
                f"Acquired LLM lock for channel {channel.id} (Trigger: {trigger_type})"
            )

            history, typing_ctx = await self._prepare_history_and_typing(
                channel, trigger_type
            )
            if history is None:
                return

            try:
                memory, emojis = await self._get_llm_inputs(channel)

                # Build LLM context
                context = self.llm_service.build_context(
                    messages=history,
                    memory=memory,
                    trigger_type=trigger_type,
                    available_emojis=emojis,
                    channel_name=channel.name,
                    is_react_only=is_react_only_channel(channel),
                )

                # Save debug prompt
                prompt = self.llm_service.build_prompt(context)
                debug_dir = Path("debug")
                debug_dir.mkdir(exist_ok=True)
                with open(
                    debug_dir / f"{channel.name}.txt", "w", encoding="utf-8"
                ) as f:
                    f.write(prompt)

                logger.info(">>> Entering LLM Generation")
                llm_response = await self.llm_service.generate_response(context)

                await self._stop_typing_context(typing_ctx)

                # Validate and process response
                llm_response = self._validate_llm_action(llm_response, history, channel)
                await self._process_llm_response(channel, llm_response)

            except Exception:
                logger.exception(
                    f"Error during BotService processing for channel {channel.id}"
                )
            finally:
                await self._stop_typing_context(typing_ctx)
                logger.debug(f"Released LLM lock for channel {channel.id}")

    async def _process_llm_response(
        self,
        channel: discord.TextChannel,
        llm_response: LLMResponse,
    ) -> None:
        """
        Update memory and execute reaction or reply based on LLMResponse.

        Args:
            channel: Discord text channel
            llm_response: LLM response to process
        """
        # Update memory if new facts were provided
        if llm_response.new_facts:
            asyncio.create_task(
                self.memory_service.update_memory(llm_response.new_facts)
            )

        # Execute actions
        if llm_response.action == LLMAction.REPLY or (
            llm_response.react_to_message_id and llm_response.reaction_emoji
        ):
            await self._execute_action(channel, llm_response)
        else:
            logger.info(
                f"LLM chose to ignore in #{channel.name}. Reason: {llm_response.reason}"
            )

    async def _execute_action(
        self,
        channel: discord.TextChannel,
        response: LLMResponse,
    ) -> None:
        """
        Handle sending replies and/or adding reactions based on LLMResponse.

        Args:
            channel: Discord text channel
            response: LLM response containing actions to execute
        """
        if response.action == LLMAction.REPLY and not response.message:
            logger.error("LLM reply was empty. Ignoring reply.")
            return

        guild = channel.guild
        if not guild:
            logger.error("Cannot execute action: No guild context.")
            return

        # Schedule reaction task
        reaction_task = None
        if response.react_to_message_id and response.reaction_emoji:
            reaction_task = asyncio.create_task(
                self._add_reaction(
                    channel,
                    response.react_to_message_id,
                    response.reaction_emoji,
                )
            )
            logger.debug(
                f"Scheduled reaction task for msg {response.react_to_message_id} "
                f"in #{channel.name}"
            )

        # Schedule reply task
        reply_task = None
        if response.action == LLMAction.REPLY and response.message:
            reply_task = asyncio.create_task(self._send_reply(channel, response))
            logger.debug(f"Scheduled reply task for channel #{channel.name}")

        # Wait for all tasks to complete
        tasks_to_wait = [t for t in [reaction_task, reply_task] if t is not None]
        if tasks_to_wait:
            await asyncio.gather(*tasks_to_wait)
            logger.debug("Finished awaiting action tasks (reaction/reply).")
        else:
            logger.debug("No reaction or reply action required by LLM response.")

    async def _add_reaction(
        self, channel: discord.TextChannel, message_id_str: str, emoji_str: str
    ) -> None:
        """
        Add a reaction to a specific message in the channel.

        Args:
            channel: Discord text channel
            message_id_str: Message ID as string
            emoji_str: Emoji string to react with
        """
        try:
            message_id = int(message_id_str)
            message_to_react = await channel.fetch_message(message_id)
            logger.info(
                f"Attempting to add reaction '{emoji_str}' to msg {message_id} "
                f"in #{channel.name}"
            )

            # Handle custom Discord emojis
            if re.match(r"<a?:\w+:\d+>", emoji_str) and channel.guild:
                emoji_parts = emoji_str.strip("<>:").split(":")
                if len(emoji_parts) >= 2:
                    emoji_id = int(emoji_parts[-1])
                    found_emoji = discord.utils.get(channel.guild.emojis, id=emoji_id)
                    if found_emoji and found_emoji.available:
                        await message_to_react.add_reaction(found_emoji)
                        logger.info(
                            f"Added custom emoji reaction {found_emoji} successfully"
                        )
                    else:
                        logger.warning(
                            f"Could not find or use custom emoji {emoji_str} on server. "
                            "Falling back to unicode attempt."
                        )
                        await message_to_react.add_reaction(emoji_str)
                        logger.info(
                            f"Added fallback unicode reaction '{emoji_str}' successfully"
                        )
                else:
                    await message_to_react.add_reaction(emoji_str)
                    logger.info(
                        f"Added standard unicode reaction '{emoji_str}' successfully"
                    )
            else:
                await message_to_react.add_reaction(emoji_str)
                logger.info(
                    f"Added standard unicode reaction '{emoji_str}' successfully"
                )

        except discord.NotFound:
            logger.warning(
                f"Cannot react: Message {message_id_str} not found in #{channel.name}."
            )
        except (ValueError, TypeError):
            logger.error(f"Cannot react: Invalid message ID format '{message_id_str}'.")
        except discord.Forbidden:
            logger.error(
                f"Cannot react: Missing 'Add Reactions' permission in #{channel.name}."
            )
        except discord.HTTPException as e:
            logger.error(
                f"Cannot react: HTTP error adding reaction '{emoji_str}' "
                f"to {message_id_str}: {e}"
            )
        except Exception as e:
            logger.exception(
                f"Unexpected error adding reaction '{emoji_str}' to {message_id_str}: {e}"
            )

    async def _send_reply(
        self,
        channel: discord.TextChannel,
        response: LLMResponse,
    ) -> None:
        """
        Handle the logic for sending a text reply with typing simulation.

        Args:
            channel: Discord text channel
            response: LLM response containing reply data
        """
        content = response.message or ""
        if not content:
            logger.error("Reply content is empty")
            return

        typing_delay_needed = self._calculate_typing_delay(content)

        async with channel.typing():
            if typing_delay_needed > 0:
                logger.debug(
                    f"Typing simulation: Sleeping for {typing_delay_needed:.2f}s"
                )
                await asyncio.sleep(typing_delay_needed)

            sent_message = None
            try:
                ref_message_obj = None
                if response.reply_to:
                    try:
                        ref_message_obj = await channel.fetch_message(
                            int(response.reply_to)
                        )
                    except discord.NotFound:
                        logger.warning(
                            f"Reply target message {response.reply_to} not found "
                            f"in #{channel.name}. Sending as normal message."
                        )
                    except (ValueError, TypeError):
                        logger.error(
                            f"Invalid reply_to ID format: {response.reply_to}. "
                            "Sending as normal message."
                        )

                logger.info(
                    f"Sending reply to #{channel.name} "
                    f"(ReplyTo: {response.reply_to or 'None'}): {content}"
                )

                if ref_message_obj is not None:
                    logger.debug(
                        f"Sending message as reply to {ref_message_obj.id} in #{channel.name}"
                    )
                    sent_message = await channel.send(
                        content, reference=ref_message_obj
                    )
                else:
                    logger.debug(
                        f"Sending message normally in #{channel.name} "
                        "(no reference specified or found)"
                    )
                    sent_message = await channel.send(content)

                logger.info(
                    f"Message {sent_message.id} sent successfully to #{channel.name}."
                )

            except discord.Forbidden:
                logger.error(
                    f"Permission error: Cannot send message to #{channel.name}."
                )
            except discord.HTTPException as e:
                logger.error(f"HTTP error sending message to #{channel.name}: {e}")
            except Exception:
                logger.exception(f"Unexpected error sending message to #{channel.name}")

            # Cache the sent message
            if sent_message and channel.guild:
                try:
                    actual_msg_dict_raw = await message_to_dict(
                        sent_message, channel.guild
                    )
                    actual_msg_dict_raw["content"] = content
                    actual_msg_dict = MessageDict.from_dict(actual_msg_dict_raw)

                    logger.debug(
                        f"Caching message {sent_message.id} in #{channel.name} "
                        "with original content."
                    )
                    await self.cache_service.add_message(channel.id, actual_msg_dict)
                except Exception as e:
                    logger.error(f"Error caching sent message: {e}")

    def _calculate_typing_delay(self, text: str) -> float:
        """
        Calculate typing delay based on config settings.

        Args:
            text: Text content to calculate delay for

        Returns:
            float: Delay in seconds
        """
        words = len(text.split())
        if words == 0:
            return 0.0

        secs_per_word = 60.0 / self.config.typing_speed_wpm
        base_delay = words * secs_per_word
        random_multiplier = random.uniform(
            self.config.typing_randomness_min, self.config.typing_randomness_max
        )
        calculated_delay = base_delay * random_multiplier
        final_delay = min(calculated_delay, self.config.typing_max_delay)

        logger.debug(
            f"Calculated typing delay for {words} words: {final_delay:.2f}s "
            f"(Base: {base_delay:.2f}s, Multiplier: {random_multiplier:.2f})"
        )
        return final_delay
