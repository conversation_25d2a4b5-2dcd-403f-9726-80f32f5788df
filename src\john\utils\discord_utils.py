"""
Discord utility functions for the John Discord bot.

This module contains utility functions for Discord operations,
including channel validation, user validation, and message processing.
"""

import asyncio
import logging
import re
from typing import Any

import discord

from ..config.settings import get_config

logger = logging.getLogger(__name__)


def is_allowed_channel(channel: discord.abc.GuildChannel | None) -> bool:
    """
    Check if a channel is configured to be allowed.
    
    Args:
        channel: Discord channel object
        
    Returns:
        bool: True if channel is allowed, False otherwise
    """
    config = get_config()
    
    # Must have at least one restriction defined
    has_restrictions = bool(
        config.allowed_server_ids or config.allowed_channel_names
    )
    
    if not has_restrictions:
        logger.warning(
            "No server or channel restrictions defined in config. "
            "Allowing all channels by default, which might be unintended."
        )
        return True
    
    if not isinstance(channel, discord.TextChannel) or not channel.guild:
        return False
    
    # Check server ID
    server_id_match = channel.guild.id in config.allowed_server_ids
    
    # Check channel name
    channel_name_match = (
        not config.allowed_channel_names  # Allow if list is empty
        or channel.name in config.allowed_channel_names
    )
    
    # Channel is allowed if both server and name match
    is_allowed = server_id_match and channel_name_match
    
    return is_allowed


def is_allowed_user(user: discord.User | discord.Member | None) -> bool:
    """
    Check if a user is allowed to use commands.
    
    Args:
        user: Discord user object
        
    Returns:
        bool: True if user is allowed, False otherwise
    """
    config = get_config()
    
    if not user or not config.allowed_user_ids:
        return False
    
    return user.id in config.allowed_user_ids


async def get_author_info(
    guild: discord.Guild, 
    author: discord.User | discord.Member
) -> dict[str, Any]:
    """
    Get the current display name and username for an author.
    
    Args:
        guild: Discord guild object
        author: Discord user/member object
        
    Returns:
        dict[str, Any]: Author information dictionary
    """
    username = author.name
    display_name = author.name
    
    # Try to get member info for display name
    member = guild.get_member(author.id)
    if member:
        display_name = member.display_name
    elif isinstance(author, discord.Member):
        display_name = author.display_name
    
    return {
        "id": author.id,
        "username": username,
        "display_name": display_name
    }


async def message_to_dict(
    message: discord.Message, 
    guild: discord.Guild
) -> dict[str, Any]:
    """
    Convert a Discord message to a dictionary suitable for the LLM.
    
    Args:
        message: Discord message object
        guild: Discord guild object
        
    Returns:
        dict[str, Any]: Message dictionary
    """
    content = message.content or ""
    
    # Get author info
    author_info_task = get_author_info(guild, message.author)
    
    # Get mention info
    mention_info_tasks = [
        get_author_info(guild, user) 
        for user in message.mentions
    ]
    
    # Execute all info gathering concurrently
    author_info, *mention_infos = await asyncio.gather(
        author_info_task, 
        *mention_info_tasks
    )
    
    # Build reaction info with users per emoji
    reactions_info: list[dict[str, Any]] = []
    for reaction in message.reactions:
        emoji_str = str(reaction.emoji)
        
        # Fetch users who reacted with this emoji
        users = [user async for user in reaction.users()]
        
        # Resolve user info for each reactor
        user_info_tasks = [get_author_info(guild, user) for user in users]
        users_info = await asyncio.gather(*user_info_tasks)
        
        reactions_info.append({
            "emoji": emoji_str,
            "users": users_info
        })
    
    return {
        "id": str(message.id),
        "author": author_info,
        "content": content,  # Raw content
        "timestamp": message.created_at.isoformat(),
        "referenced_message": str(message.reference.message_id)
        if message.reference
        else None,
        "mentions": mention_infos,
        "attachments": [attachment.url for attachment in message.attachments],
        "reactions": reactions_info,
    }


def clean_message_content(message_content: str | None) -> str:
    """
    Remove spoilers and trim whitespace from message content.
    
    Args:
        message_content: Raw message content
        
    Returns:
        str: Cleaned content or empty string if invalid
    """
    if not message_content:
        return ""
    
    # Remove spoiler tags
    content_no_spoilers = re.sub(
        r"\|\|(.*?)\|\|", 
        "", 
        message_content, 
        flags=re.DOTALL
    )
    
    return content_no_spoilers.strip()


def is_react_only_channel(channel: discord.TextChannel) -> bool:
    """
    Check if a channel is configured as react-only.
    
    Args:
        channel: Discord text channel
        
    Returns:
        bool: True if channel is react-only, False otherwise
    """
    config = get_config()
    return channel.name in config.react_only_channels


def can_react_to_message(
    message_dict: dict[str, Any], 
    history: list[dict[str, Any]]
) -> bool:
    """
    Check if the bot can react to a message based on recency limits.
    
    Args:
        message_dict: Message dictionary to check
        history: List of recent message dictionaries
        
    Returns:
        bool: True if message is recent enough to react to
    """
    config = get_config()
    
    if not message_dict.get("id"):
        return False
    
    # Get the most recent messages (sorted by timestamp, newest last)
    recent_messages = history[-config.max_recent_messages_to_react:]
    recent_message_ids = [
        msg.get("id") for msg in recent_messages 
        if msg.get("id")
    ]
    
    return message_dict["id"] in recent_message_ids
