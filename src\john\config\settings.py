"""
Settings and configuration management for the John Discord bot.

This module provides a centralized way to access bot configuration
with validation and type safety.
"""

from functools import lru_cache
from ..models.config_models import BotConfig


@lru_cache(maxsize=1)
def get_config() -> BotConfig:
    """
    Get the bot configuration instance.
    
    This function is cached to ensure we only create one configuration
    instance throughout the application lifecycle.
    
    Returns:
        BotConfig: The validated configuration instance
    """
    return BotConfig()
