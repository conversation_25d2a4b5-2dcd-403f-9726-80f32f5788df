[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "john"
version = "0.2.0"
description = "John - An LLM-powered Discord bot with personality and memory"
readme = "README.md"
authors = [
  { name = "Andy Pilate", email = "<EMAIL>" }
]
requires-python = ">=3.11,<4.0"
dependencies = [
  "python-dotenv>=1.1.0",
  "discord>=2.3.2",
  "google-genai>=1.11.0",
  "pytz>=2025.2",
  "pydantic>=2.0.0",
  "structlog>=24.0.0",
]

[project.optional-dependencies]
dev = [
  "pytest>=8.0.0",
  "pytest-asyncio>=0.23.0",
  "pytest-mock>=3.12.0",
  "black>=24.0.0",
  "ruff>=0.1.0",
  "mypy>=1.8.0",
]

[tool.uv]
package = false

[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
  "E",  # pycodestyle errors
  "W",  # pycodestyle warnings
  "F",  # pyflakes
  "I",  # isort
  "B",  # flake8-bugbear
  "C4", # flake8-comprehensions
  "UP", # pyupgrade
]
ignore = [
  "E501", # line too long, handled by black
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
