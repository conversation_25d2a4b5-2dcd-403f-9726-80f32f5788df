"""
Message-related models for the John Discord bot.

This module contains Pydantic models for Discord messages,
authors, reactions, and other message-related data structures.
"""

from typing import Any
from pydantic import BaseModel, Field


class AuthorInfo(BaseModel):
    """Model for Discord message author information."""
    
    id: int = Field(
        ...,
        description="Discord user ID"
    )
    username: str = Field(
        ...,
        description="Discord username"
    )
    display_name: str = Field(
        ...,
        description="Display name in the server"
    )


class ReactionInfo(BaseModel):
    """Model for Discord message reaction information."""
    
    emoji: str = Field(
        ...,
        description="Emoji used for the reaction"
    )
    users: list[AuthorInfo] = Field(
        default_factory=list,
        description="Users who reacted with this emoji"
    )


class MessageDict(BaseModel):
    """Model for Discord message data."""
    
    id: str = Field(
        ...,
        description="Discord message ID"
    )
    author: AuthorInfo = Field(
        ...,
        description="Message author information"
    )
    content: str = Field(
        default="",
        description="Message content"
    )
    timestamp: str = Field(
        ...,
        description="Message timestamp in ISO format"
    )
    referenced_message: str | None = Field(
        default=None,
        description="ID of referenced message (for replies)"
    )
    mentions: list[AuthorInfo] = Field(
        default_factory=list,
        description="Users mentioned in the message"
    )
    attachments: list[str] = Field(
        default_factory=list,
        description="List of attachment URLs"
    )
    reactions: list[ReactionInfo] = Field(
        default_factory=list,
        description="List of reactions on the message"
    )
    
    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "MessageDict":
        """Create MessageDict from a dictionary."""
        # Convert author data
        author_data = data.get("author", {})
        author = AuthorInfo(
            id=author_data.get("id", 0),
            username=author_data.get("username", ""),
            display_name=author_data.get("display_name", "")
        )
        
        # Convert mentions data
        mentions_data = data.get("mentions", [])
        mentions = [
            AuthorInfo(
                id=mention.get("id", 0),
                username=mention.get("username", ""),
                display_name=mention.get("display_name", "")
            )
            for mention in mentions_data
        ]
        
        # Convert reactions data
        reactions_data = data.get("reactions", [])
        reactions = []
        for reaction in reactions_data:
            users_data = reaction.get("users", [])
            users = [
                AuthorInfo(
                    id=user.get("id", 0),
                    username=user.get("username", ""),
                    display_name=user.get("display_name", "")
                )
                for user in users_data
            ]
            reactions.append(ReactionInfo(
                emoji=reaction.get("emoji", ""),
                users=users
            ))
        
        return cls(
            id=str(data.get("id", "")),
            author=author,
            content=data.get("content", ""),
            timestamp=data.get("timestamp", ""),
            referenced_message=data.get("referenced_message"),
            mentions=mentions,
            attachments=data.get("attachments", []),
            reactions=reactions
        )
    
    def to_dict(self) -> dict[str, Any]:
        """Convert MessageDict to a dictionary."""
        return {
            "id": self.id,
            "author": {
                "id": self.author.id,
                "username": self.author.username,
                "display_name": self.author.display_name
            },
            "content": self.content,
            "timestamp": self.timestamp,
            "referenced_message": self.referenced_message,
            "mentions": [
                {
                    "id": mention.id,
                    "username": mention.username,
                    "display_name": mention.display_name
                }
                for mention in self.mentions
            ],
            "attachments": self.attachments,
            "reactions": [
                {
                    "emoji": reaction.emoji,
                    "users": [
                        {
                            "id": user.id,
                            "username": user.username,
                            "display_name": user.display_name
                        }
                        for user in reaction.users
                    ]
                }
                for reaction in self.reactions
            ]
        }
