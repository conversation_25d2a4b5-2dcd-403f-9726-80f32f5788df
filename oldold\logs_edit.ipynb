{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "\n", "with open(\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\a\\\\weirdos_big.json\", encoding= \"utf-8\") as file:\n", "    json_data = json.load(file)\n", "\n", "\n", "    # message_types = set(message[\"type\"] for message in json_data[\"messages\"])\n", "    # print(\"Possible values for message['type']:\")\n", "    # for message_type in message_types:\n", "    #     print(message_type)\n", "\n", "\n", "    for message in json_data[\"messages\"]:\n", "        del message[\"attachments\"]\n", "        del message[\"stickers\"]\n", "        del message[\"author\"][\"roles\"]\n", "        del message[\"author\"][\"avatarUrl\"]\n", "        del message[\"author\"][\"discriminator\"]\n", "        del message[\"author\"][\"color\"]\n", "        del message[\"callEndedTimestamp\"]\n", "        for mention in message[\"mentions\"]:\n", "            del mention[\"roles\"]\n", "            del mention[\"avatarUrl\"]\n", "            del mention[\"discriminator\"]\n", "            del mention[\"color\"]\n", "\n", "        for reaction in message[\"reactions\"]:\n", "            del reaction[\"emoji\"][\"isAnimated\"]\n", "            del reaction[\"emoji\"][\"imageUrl\"]\n", "            for user in reaction[\"users\"]:\n", "                del user[\"avatarUrl\"]\n", "                del user[\"discriminator\"]\n", "                del user[\"isBot\"]\n", "\n", "    with open(\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\a\\\\weirdos_big_stripped.json\", \"w\", encoding=\"utf-8\") as file:\n", "        json.dump(json_data, file, indent=2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}