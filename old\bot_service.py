import asyncio
import logging
import discord
from typing import Dict, Any, Optional, List
import random
import time
import re
from datetime import datetime
import pathlib

from config import config
from message_cache import message_cache_manager
from memory_manager import memory_manager
from llm_interface import llm_interface, LLMResponse
from discord_utils import message_to_dict

logger = logging.getLogger(__name__)


class BotService:
    def __init__(self):
        self._processing_locks: Dict[int, asyncio.Lock] = {}
        self._self_id: Optional[int] = None
        self._last_check_times: Dict[int, float] = {}

    def set_self_id(self, user_id: int):
        """Stores the bot's own user ID after login."""
        self._self_id = user_id
        logger.info(f"Bot Service knows Self ID: {self._self_id}")

    async def _get_lock(self, channel_id: int) -> asyncio.Lock:
        """Gets or creates the processing lock for a channel."""
        if channel_id not in self._processing_locks:
            self._processing_locks[channel_id] = asyncio.Lock()
        return self._processing_locks[channel_id]

    def _is_react_only_channel(self, channel: discord.TextChannel) -> bool:
        """Checks if a channel is configured as react-only."""
        return channel.name in config.react_only_channels

    def _can_react_to_message(
        self, message_dict: Dict[str, Any], history: List[Dict[str, Any]]
    ) -> bool:
        """Checks if the bot can react to a message based on recency limits."""
        if not message_dict.get("id"):
            return False

        # Get the most recent messages (sorted by timestamp, newest last)
        recent_messages = history[-config.max_recent_messages_to_react :]
        recent_message_ids = [msg.get("id") for msg in recent_messages if msg.get("id")]

        return message_dict["id"] in recent_message_ids

    def _validate_llm_action(
        self,
        data: LLMResponse,
        history: List[Dict[str, Any]],
        channel: discord.TextChannel,
    ) -> LLMResponse:
        """Validates the LLM action, preventing duplicate replies, self-replies, and enforcing react-only channels."""
        # Check if this is a react-only channel and prevent replies
        if self._is_react_only_channel(channel) and data.action == "reply":
            logger.warning(
                f"LLM attempted to reply in react-only channel #{channel.name}. Overriding to 'ignore'."
            )
            data.action = "ignore"
            data.message = None
            data.reason += " (Forced ignore: react-only channel)"
            return data

        # Prevent self-replies
        if data.action == "reply" and data.reply_to and self._self_id:
            original_message = next(
                (msg for msg in history if msg.get("id") == data.reply_to), None
            )

            if (
                original_message
                and original_message.get("author", {}).get("id") == self._self_id
            ):
                logger.warning(
                    f"LLM attempted to reply to its own message ({data.reply_to}). Overriding to 'ignore' by self-reply prevention."
                )
                data.action = "ignore"
                data.message = None
                data.reason += " (Forced ignore: cannot reply to self)"
                return data

        # Validate reactions in react-only channels are to recent messages
        if (
            self._is_react_only_channel(channel)
            and data.react_to_message_id
            and data.reaction_emoji
        ):
            target_message = next(
                (msg for msg in history if msg.get("id") == data.react_to_message_id),
                None,
            )
            if target_message and not self._can_react_to_message(
                target_message, history
            ):
                logger.warning(
                    f"LLM attempted to react to old message {data.react_to_message_id} in react-only channel #{channel.name}. Overriding to 'ignore'."
                )
                data.action = "ignore"
                data.react_to_message_id = None
                data.reaction_emoji = None
                data.reason += " (Forced ignore: message too old for reaction)"
                return data

        return data

    async def _get_combined_history(
        self, source_channel: discord.TextChannel
    ) -> List[Dict[str, Any]]:
        """Fetches history from source channel."""
        source_history = await message_cache_manager.get_history(source_channel.id)
        return list(source_history)

    async def check_channel_inactivity(self, source_channel: discord.TextChannel):
        """Checks if a source channel has been inactive."""
        if not source_channel.guild:
            return

        source_channel_id = source_channel.id

        source_ts = await message_cache_manager.get_last_message_timestamp(
            source_channel_id
        )

        if source_ts is None or source_ts == 0.0:
            logger.debug(
                f"Skipping scheduled check for #{source_channel.name}: No activity recorded."
            )
            return

        last_check_ts = self._last_check_times.get(source_channel_id, 0.0)

        current_time = time.time()
        inactive_threshold = config.inactivity_threshold
        time_since_last_message = current_time - source_ts
        time_since_last_check = current_time - last_check_ts

        should_trigger = (time_since_last_message > inactive_threshold) and (
            time_since_last_check > inactive_threshold
        )

        if should_trigger:
            logger.info(
                f"Channel context #{source_channel.name} inactive for >{inactive_threshold}s "
                f"(Last msg: {datetime.fromtimestamp(source_ts).isoformat()}, "
                f"Last check: {datetime.fromtimestamp(last_check_ts).isoformat()}). "
                f"Triggering scheduled LLM check."
            )
            self._last_check_times[source_channel_id] = current_time

            asyncio.create_task(
                self.process_channel_event(
                    source_channel, trigger_type="scheduled_check"
                )
            )
        else:
            if not (time_since_last_message > inactive_threshold):
                logger.debug(
                    f"Skipping scheduled check for #{source_channel.name}: Activity within last {inactive_threshold}s ({time_since_last_message:.0f}s ago)."
                )
            elif not (time_since_last_check > inactive_threshold):
                logger.debug(
                    f"Skipping scheduled check for #{source_channel.name}: Already checked within last {inactive_threshold}s ({time_since_last_check:.0f}s ago)."
                )

    async def _prepare_history_and_typing(
        self, channel: discord.TextChannel, trigger_type: str
    ):
        """Fetch history, handle empty case, and start typing if needed."""
        history = await self._get_combined_history(channel)
        if not history and trigger_type == "new_message":
            logger.warning(
                f"Processing new message for #{channel.name} but combined cache was empty. Retrying fetch."
            )
            history = await self._get_combined_history(channel)
            if not history:
                logger.error(
                    f"Failed to get any history for #{channel.name} after retry."
                )
                return None, None, None

        typing_ctx = None
        # Don't start typing in react-only channels since we won't be replying
        if trigger_type != "scheduled_check" and not self._is_react_only_channel(
            channel
        ):
            typing_ctx = channel.typing()
            await typing_ctx.__aenter__()
            logger.debug(f"Started typing for {trigger_type} in #{channel.name}")
        return history, typing_ctx, channel

    async def _stop_typing_context(self, typing_ctx):
        """Stops typing context if active."""
        if typing_ctx:
            try:
                await typing_ctx.__aexit__(None, None, None)
            except Exception as e:
                logger.error(f"Error stopping typing context: {e}")

    async def _get_llm_inputs(self, channel: discord.TextChannel):
        """Retrieve memory snapshot and available custom emojis."""
        memory = await memory_manager.get_memory_snapshot()
        emojis: List[str] = []
        if channel.guild:
            emojis = [str(e) for e in channel.guild.emojis if e.available]
            logger.debug(
                f"Providing {len(emojis)} available custom emojis to LLM for #{channel.name}."
            )
        else:
            logger.warning(
                f"Cannot fetch custom emojis for #{channel.name}: No guild context."
            )
        return memory, emojis

    async def _generate_llm_response(
        self,
        history: List[Dict[str, Any]],
        memory: Dict[str, Any],
        trigger_type: str,
        emojis: List[str],
        channel: discord.TextChannel,
    ):
        """Invoke LLM and measure call duration."""
        start = time.monotonic()
        response = await llm_interface.generate_response(
            history, memory, trigger_type, emojis, channel
        )
        duration = time.monotonic() - start
        return response, duration

    async def _process_llm_response(
        self,
        channel: discord.TextChannel,
        llm_response: LLMResponse,
        llm_duration: float,
    ):
        """Update memory, and execute reaction or reply based on LLMResponse."""
        if llm_response.new_facts:
            asyncio.create_task(memory_manager.update_memory(llm_response.new_facts))
        if llm_response.action == "reply" or (
            llm_response.react_to_message_id and llm_response.reaction_emoji
        ):
            await self._execute_action(channel, llm_response, llm_duration)
        else:
            logger.info(
                f"LLM chose to ignore in #{channel.name}. Reason: {llm_response.reason}"
            )

    async def process_channel_event(
        self, channel: discord.TextChannel, trigger_type: str
    ):
        lock = await self._get_lock(channel.id)

        async with lock:
            logger.debug(
                f"Acquired LLM lock for channel {channel.id} (Trigger: {trigger_type})"
            )
            history, typing_ctx, _ = await self._prepare_history_and_typing(
                channel, trigger_type
            )
            if history is None:
                return

            try:
                memory, emojis = await self._get_llm_inputs(channel)
                prompt = llm_interface._build_prompt(
                    history, memory, trigger_type, emojis
                )
                debug_dir = pathlib.Path("debug")
                debug_dir.mkdir(exist_ok=True)
                with open(
                    debug_dir / f"{channel.name}.txt", "w", encoding="utf-8"
                ) as f:
                    f.write(prompt)
                logger.info(">>> Entering LLM Generation")
                llm_response, llm_duration = await self._generate_llm_response(
                    history, memory, trigger_type, emojis, channel
                )
                await self._stop_typing_context(typing_ctx)
                llm_response = self._validate_llm_action(llm_response, history, channel)
                await self._process_llm_response(channel, llm_response, llm_duration)
            except Exception:
                logger.exception(
                    f"Error during BotService processing for channel {channel.id}"
                )
            finally:
                await self._stop_typing_context(typing_ctx)
                logger.debug(f"Released LLM lock for channel {channel.id}")

    async def _execute_action(
        self,
        original_channel: discord.TextChannel,
        response: LLMResponse,
        llm_duration: float,
    ):
        """Handles sending replies and/or adding reactions based on LLMResponse."""
        content = response.message or ""
        if response.action == "reply" and not content:
            logger.error("LLM reply was empty. Ignoring reply.")
            return

        guild = original_channel.guild
        if not guild:
            logger.error("Cannot execute action: No guild context.")
            return

        reaction_task = None
        if response.react_to_message_id and response.reaction_emoji:
            reaction_task = asyncio.create_task(
                self._add_reaction(
                    original_channel,
                    response.react_to_message_id,
                    response.reaction_emoji,
                )
            )
            logger.debug(
                f"Scheduled reaction task for msg {response.react_to_message_id} in #{original_channel.name}"
            )

        reply_task = None
        if response.action == "reply" and response.message:
            reply_task = asyncio.create_task(
                self._send_reply(
                    original_channel, original_channel, response, llm_duration
                )
            )
            logger.debug(f"Scheduled reply task for channel #{original_channel.name}")

        tasks_to_wait = [t for t in [reaction_task, reply_task] if t is not None]
        if tasks_to_wait:
            await asyncio.gather(*tasks_to_wait)
            logger.debug("Finished awaiting action tasks (reaction/reply).")
        else:
            logger.debug("No reaction or reply action required by LLM response.")

    async def _add_reaction(
        self, original_channel: discord.TextChannel, message_id_str: str, emoji_str: str
    ):
        """Adds a reaction to a specific message in the original channel."""
        try:
            message_id = int(message_id_str)
            message_to_react = await original_channel.fetch_message(message_id)
            logger.info(
                f"Attempting to add reaction '{emoji_str}' to msg {message_id} in #{original_channel.name}"
            )

            if re.match(r"<a?:\w+:\d+>", emoji_str) and original_channel.guild:
                emoji_name_id = emoji_str.strip("<>:").split(":")[-2:]
                emoji_id = int(emoji_name_id[1])
                found_emoji = discord.utils.get(
                    original_channel.guild.emojis, id=emoji_id
                )
                if found_emoji and found_emoji.available:
                    await message_to_react.add_reaction(found_emoji)
                    logger.info(
                        f"Added custom emoji reaction {found_emoji} successfully."
                    )
                else:
                    logger.warning(
                        f"Could not find or use custom emoji {emoji_str} on server. Falling back to unicode attempt."
                    )
                    await message_to_react.add_reaction(emoji_str)
                    logger.info(
                        f"Added fallback unicode reaction '{emoji_str}' successfully."
                    )
            else:
                await message_to_react.add_reaction(emoji_str)
                logger.info(
                    f"Added standard unicode reaction '{emoji_str}' successfully."
                )

        except discord.NotFound:
            logger.warning(
                f"Cannot react: Message {message_id_str} not found in #{original_channel.name}."
            )
        except (ValueError, TypeError):
            logger.error(f"Cannot react: Invalid message ID format '{message_id_str}'.")
        except discord.Forbidden:
            logger.error(
                f"Cannot react: Missing 'Add Reactions' permission in #{original_channel.name}."
            )
        except discord.HTTPException as e:
            logger.error(
                f"Cannot react: HTTP error adding reaction '{emoji_str}' to {message_id_str}: {e}"
            )
        except Exception as e:
            logger.exception(
                f"Unexpected error adding reaction '{emoji_str}' to {message_id_str}: {e}"
            )

    async def _send_reply(
        self,
        original_channel: discord.TextChannel,
        target_channel: discord.TextChannel,
        response: LLMResponse,
        llm_duration: float,
    ):
        """Handles the logic for sending a text reply, including typing, sanitization, and caching."""
        content = response.message or ""
        content_to_send = content

        typing_delay_needed = self._calculate_typing_delay(content)
        remaining_delay = typing_delay_needed - llm_duration

        async with target_channel.typing():
            if remaining_delay > 0:
                logger.debug(f"Typing simulation: Sleeping for {remaining_delay:.2f}s")
                await asyncio.sleep(remaining_delay)
            else:
                logger.debug(
                    f"Typing simulation: LLM took longer ({llm_duration:.2f}s) than calculated delay ({typing_delay_needed:.2f}s). Sending immediately."
                )

            sent_message = None
            try:
                ref_message_obj = None
                if response.reply_to:
                    try:
                        ref_message_obj = await original_channel.fetch_message(
                            int(response.reply_to)
                        )
                    except discord.NotFound:
                        logger.warning(
                            f"Reply target message {response.reply_to} not found in #{original_channel.name}. Sending as normal message."
                        )
                    except (ValueError, TypeError):
                        logger.error(
                            f"Invalid reply_to ID format: {response.reply_to}. Sending as normal message."
                        )

                logger.info(
                    f"Sending reply to #{target_channel.name} (ReplyTo: {response.reply_to or 'None'}): {content_to_send}"
                )

                if ref_message_obj is not None:
                    logger.debug(
                        f"Sending message as reply to {ref_message_obj.id} in #{target_channel.name}"
                    )
                    sent_message = await target_channel.send(
                        content_to_send, reference=ref_message_obj
                    )
                else:
                    logger.debug(
                        f"Sending message normally in #{target_channel.name} (no reference specified or found)"
                    )
                    sent_message = await target_channel.send(content_to_send)

                logger.info(
                    f"Message {sent_message.id} sent successfully to #{target_channel.name}."
                )

            except discord.Forbidden:
                logger.error(
                    f"Permission error: Cannot send message to #{target_channel.name}."
                )
            except discord.HTTPException as e:
                logger.error(
                    f"HTTP error sending message to #{target_channel.name}: {e}"
                )
            except Exception:
                logger.exception(
                    f"Unexpected error sending message to #{target_channel.name}"
                )

            if sent_message:
                guild = target_channel.guild
                if not guild:
                    logger.error(
                        "Cannot cache sent message: No guild context for target channel."
                    )
                    return

                actual_msg_dict = await message_to_dict(sent_message, guild)
                actual_msg_dict["content"] = content

                logger.debug(
                    f"Caching message {sent_message.id} in #{target_channel.name} with original content."
                )
                await message_cache_manager.add_message(
                    target_channel.id, actual_msg_dict
                )

    def _calculate_typing_delay(self, text: str) -> float:
        """Calculates typing delay based on config settings."""
        words = len(text.split())
        if words == 0:
            return 0.0
        secs_per_word = 60.0 / config.typing_speed_wpm
        base_delay = words * secs_per_word
        random_multiplier = random.uniform(
            config.typing_randomness_min, config.typing_randomness_max
        )
        calculated_delay = base_delay * random_multiplier
        final_delay = min(calculated_delay, config.typing_max_delay)
        logger.debug(
            f"Calculated typing delay for {words} words: {final_delay:.2f}s (Base: {base_delay:.2f}s, Multiplier: {random_multiplier:.2f})"
        )
        return final_delay


bot_service = BotService()
