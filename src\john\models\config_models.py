"""
Configuration models for the John Discord bot.

This module contains Pydantic models for bot configuration,
providing validation and type safety for all settings.
"""

import os
from pathlib import Path
from typing import Any
from pydantic import BaseModel, Field, field_validator
from dotenv import load_dotenv


# Load .env file from project root
# Look for .env file in current directory and parent directories
def _load_env() -> None:
    """Load environment variables from .env file."""
    current_dir = Path.cwd()

    # Try current directory first
    env_file = current_dir / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"Loaded .env from: {env_file}")
        return

    # Try parent directories (up to 3 levels)
    for parent in current_dir.parents[:3]:
        env_file = parent / ".env"
        if env_file.exists():
            load_dotenv(env_file)
            print(f"Loaded .env from: {env_file}")
            return

    # If no .env file found, just call load_dotenv() to load from environment
    load_dotenv()
    print(f"No .env file found in {current_dir} or parent directories")


_load_env()


class BotConfig(BaseModel):
    """Configuration model for the John Discord bot."""

    # Discord settings
    discord_token: str = Field(default="", description="Discord bot token")
    allowed_server_ids: list[int] = Field(
        default=[864227641111347221], description="List of allowed Discord server IDs"
    )
    allowed_channel_names: list[str] = Field(
        default=["john", "weirdos", "degenerates", "talk"],
        description="List of allowed channel names",
    )
    allowed_user_ids: list[int] = Field(
        default=[140880190158012417],
        description="List of allowed user IDs for commands",
    )

    # React-only channels configuration
    react_only_channels: list[str] = Field(
        default=["weirdos", "degenerates", "talk"],
        description="Channels where bot can only react, not reply",
    )
    max_recent_messages_to_react: int = Field(
        default=5, description="Maximum number of recent messages bot can react to"
    )

    # Channel cutoff configuration
    channel_cutoff_map: dict[str, int] = Field(
        default={
            "weirdos": 1365073857676972093,
            "john": 1376281207981084823,
        },
        description="Map of channel names to message ID cutoffs",
    )

    # LLM settings
    google_api_key: str | None = Field(
        default="", description="Google AI API key for LLM"
    )
    llm_model_name: str = Field(
        default="gemini-2.5-flash-preview-05-20",
        description="Name of the LLM model to use",
    )
    max_tokens: int = Field(default=64000, description="Maximum tokens for LLM context")

    # Bot behavior settings
    test_mode: bool = Field(default=False, description="Enable test mode for debugging")
    initial_fetch_limit: int = Field(
        default=250, description="Number of messages to fetch on startup"
    )

    # Memory settings
    memory_path: str = Field(
        default="memory.json", description="Path to the memory file"
    )

    # Typing simulation settings
    typing_speed_wpm: int = Field(
        default=150, description="Typing speed in words per minute"
    )
    typing_max_delay: int = Field(
        default=10, description="Maximum typing delay in seconds"
    )
    typing_randomness_min: float = Field(
        default=0.8, description="Minimum randomness multiplier for typing"
    )
    typing_randomness_max: float = Field(
        default=1.2, description="Maximum randomness multiplier for typing"
    )

    # Inactivity settings
    inactivity_threshold: float = Field(
        default=3600.0, description="Seconds before inactivity check triggers"
    )

    # Timezone settings
    local_timezone: str = Field(
        default="Europe/Paris", description="Local timezone for time context"
    )

    @field_validator("discord_token", mode="before")
    @classmethod
    def validate_discord_token(cls, v: Any) -> str:
        """Validate Discord token from environment."""
        if isinstance(v, str) and v.strip():
            return v

        token = os.getenv("DISCORD_BOT_TOKEN")
        if not token:
            # Debug information
            current_dir = Path.cwd()
            env_vars = [k for k in os.environ.keys() if "DISCORD" in k or "TOKEN" in k]
            raise ValueError(
                f"DISCORD_BOT_TOKEN environment variable is required.\n"
                f"Current directory: {current_dir}\n"
                f"Environment variables containing 'DISCORD' or 'TOKEN': {env_vars}\n"
                f"Make sure you have a .env file with DISCORD_BOT_TOKEN=your_token"
            )
        return token

    @field_validator("google_api_key", mode="before")
    @classmethod
    def validate_google_api_key(cls, v: Any) -> str | None:
        """Validate Google API key from environment."""
        if isinstance(v, str) and v.strip():
            return v

        # Try both possible environment variable names
        api_key = os.getenv("GOOGLE_AI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        return api_key

    def model_post_init(self, __context: Any) -> None:
        """Post-initialization validation and warnings."""
        if not self.google_api_key:
            import logging

            logger = logging.getLogger(__name__)
            logger.warning("Google API key not found. LLM features will not work.")


def get_config() -> BotConfig:
    """Get the bot configuration instance."""
    return BotConfig()
