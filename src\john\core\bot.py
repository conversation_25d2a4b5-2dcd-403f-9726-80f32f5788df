"""
Main bot class for the John Discord bot.

This module contains the JohnBot class which coordinates
all bot functionality and manages the Discord client.
"""

import asyncio
import logging
import sys
from typing import Any

import discord
from discord import app_commands, Intents, Client, TextChannel
from discord.ext import tasks

from ..config.settings import get_config
from ..services.bot_service import BotService
from ..utils.discord_utils import is_allowed_channel, is_allowed_user
from .events import EventHandler

logger = logging.getLogger(__name__)


class JohnBot:
    """Main bot class that coordinates all functionality."""
    
    def __init__(self) -> None:
        """Initialize the John Discord bot."""
        self.config = get_config()
        
        # Set up Discord intents
        intents = Intents.default()
        intents.members = True  # Required for get_member, display names
        intents.message_content = True  # Required to read message content
        
        # Create Discord client
        self.client = Client(intents=intents)
        self.tree = app_commands.CommandTree(self.client)
        
        # Initialize services
        self.bot_service = BotService()
        self.event_handler = EventHandler(self.bot_service)
        
        # Set up event handlers
        self._setup_events()
        self._setup_commands()
        self._setup_tasks()
    
    def _setup_events(self) -> None:
        """Set up Discord event handlers."""
        
        @self.client.event
        async def on_ready() -> None:
            await self._on_ready()
        
        @self.client.event
        async def on_message(message: discord.Message) -> None:
            await self.event_handler.on_message(message)
        
        @self.client.event
        async def on_message_delete(message: discord.Message) -> None:
            await self.event_handler.on_message_delete(message)
        
        @self.client.event
        async def on_message_edit(
            before: discord.Message, 
            after: discord.Message
        ) -> None:
            await self.event_handler.on_message_edit(before, after)
        
        @self.client.event
        async def on_raw_reaction_add(
            payload: discord.RawReactionActionEvent
        ) -> None:
            await self.event_handler.on_raw_reaction_add(payload)
        
        @self.client.event
        async def on_raw_reaction_remove(
            payload: discord.RawReactionActionEvent
        ) -> None:
            await self.event_handler.on_raw_reaction_remove(payload)
    
    def _setup_commands(self) -> None:
        """Set up slash commands."""
        
        @self.tree.command(name="ping", description="Check if John is alive")
        async def ping_slash(interaction: discord.Interaction) -> None:
            if not is_allowed_user(interaction.user):
                await interaction.response.send_message("Unauthorized.", ephemeral=True)
                return
            await interaction.response.send_message("Pong!", ephemeral=True)
        
        @self.tree.command(
            name="llm", 
            description="Manually trigger LLM processing on channel history"
        )
        async def llm_slash(interaction: discord.Interaction) -> None:
            if not is_allowed_user(interaction.user):
                await interaction.response.send_message("Unauthorized.", ephemeral=True)
                return
            
            # Ensure channel is a TextChannel and is allowed
            if not isinstance(interaction.channel, TextChannel) or not is_allowed_channel(
                interaction.channel
            ):
                await interaction.response.send_message(
                    "This command cannot be used in this channel.", ephemeral=True
                )
                return
            
            await interaction.response.defer(ephemeral=True, thinking=True)
            logger.info(
                f"Manual LLM trigger initiated by {interaction.user.name} "
                f"in #{interaction.channel.name}"
            )
            
            # Run in background task
            asyncio.create_task(
                self.bot_service.process_channel_event(
                    interaction.channel, trigger_type="slash_command"
                )
            )
            await interaction.followup.send("LLM processing triggered.", ephemeral=True)
    
    def _setup_tasks(self) -> None:
        """Set up periodic tasks."""
        
        @tasks.loop(minutes=1)  # Check every minute
        async def periodic_inactivity_checker() -> None:
            logger.debug("Running periodic inactivity check...")
            check_tasks = []
            
            for server_id in self.config.allowed_server_ids:
                guild = self.client.get_guild(server_id)
                if not guild:
                    logger.warning(
                        f"Periodic check: allowed server {server_id} not found."
                    )
                    continue
                
                # Iterate through the primary SOURCE channels configured
                for channel_name in self.config.allowed_channel_names:
                    # Skip react-only channels for periodic inactivity checks
                    if channel_name in self.config.react_only_channels:
                        logger.debug(
                            f"Skipping periodic inactivity check for react-only channel: "
                            f"#{channel_name}"
                        )
                        continue
                    
                    channel = discord.utils.get(guild.text_channels, name=channel_name)
                    if channel:
                        # Delegate the check logic to the bot service
                        check_tasks.append(
                            self.bot_service.check_channel_inactivity(channel)
                        )
                    else:
                        logger.warning(
                            f"Periodic check: Source channel '{channel_name}' not found "
                            f"in guild {server_id}."
                        )
            
            if check_tasks:
                await asyncio.gather(*check_tasks)
                logger.debug(
                    "Periodic inactivity check finished for configured source channels."
                )
            else:
                logger.debug(
                    "Periodic inactivity check: No source channels found to check."
                )
        
        @periodic_inactivity_checker.before_loop
        async def before_periodic_check() -> None:
            await self.client.wait_until_ready()
            logger.info("Bot ready, periodic inactivity checker starting soon.")
        
        self.periodic_inactivity_checker = periodic_inactivity_checker
    
    async def _on_ready(self) -> None:
        """Handle bot ready event."""
        if self.client.user is None:
            logger.critical("Bot user information not available on ready. Cannot proceed.")
            return
        
        logger.info(f"Logged in as {self.client.user.name} (ID: {self.client.user.id})")
        logger.info(f"Test Mode: {self.config.test_mode}")
        logger.info(f"Allowed Servers: {self.config.allowed_server_ids}")
        logger.info(f"Allowed Channels: {self.config.allowed_channel_names}")
        logger.info(f"React-Only Channels: {self.config.react_only_channels}")
        
        # Initialize services
        self.bot_service.set_self_id(self.client.user.id)
        await self.bot_service.initialize()
        
        # Populate initial cache
        await self._populate_initial_cache()
        
        # Sync commands
        await self._sync_commands()
        
        # Queue startup LLM checks
        logger.info("Triggering initial LLM check for configured channels on startup...")
        await self._queue_startup_llm_checks()
        
        # Start the periodic inactivity checker
        self.periodic_inactivity_checker.start()
        logger.info("Periodic inactivity checker started.")
    
    async def _populate_initial_cache(self) -> None:
        """Populate initial message cache for all allowed channels."""
        cache_tasks = []
        
        for server_id in self.config.allowed_server_ids:
            guild = self.client.get_guild(server_id)
            if not guild:
                logger.error(f"Could not find allowed guild with ID: {server_id}")
                continue
            
            for ch_name in self.config.allowed_channel_names:
                ch = discord.utils.get(guild.text_channels, name=ch_name)
                if ch:
                    cutoff = self.config.channel_cutoff_map.get(ch.name)
                    cache_tasks.append(
                        self.bot_service.cache_service.populate_initial_cache(
                            ch, self.config.initial_fetch_limit, start_message_id=cutoff
                        )
                    )
        
        if cache_tasks:
            await asyncio.gather(*cache_tasks)
            logger.info("Initial cache population completed.")
        else:
            logger.warning("No channels queued for initial cache.")
    
    async def _sync_commands(self) -> None:
        """Sync slash commands for allowed servers."""
        try:
            for server_id in self.config.allowed_server_ids:
                await self.tree.sync(guild=discord.Object(id=server_id))
            logger.info("Commands synced for allowed servers.")
        except Exception as e:
            logger.error(f"Error syncing commands: {e}")
    
    async def _queue_startup_llm_checks(self) -> None:
        """Queue startup LLM checks for non-react-only channels."""
        startup_tasks = []
        
        for server_id in self.config.allowed_server_ids:
            guild = self.client.get_guild(server_id)
            if not guild:
                continue
            
            for channel_name in self.config.allowed_channel_names:
                # Skip react-only channels for startup LLM checks
                if channel_name in self.config.react_only_channels:
                    logger.info(
                        f"Skipping startup LLM check for react-only channel: #{channel_name}"
                    )
                    continue
                
                channel = discord.utils.get(guild.text_channels, name=channel_name)
                if channel:
                    startup_tasks.append(
                        self.bot_service.process_channel_event(
                            channel, trigger_type="bot_startup"
                        )
                    )
        
        if startup_tasks:
            asyncio.gather(*startup_tasks)
            logger.info("Startup LLM checks queued.")
        else:
            logger.info(
                "No startup LLM checks queued (all channels are react-only or not found)."
            )
    
    async def run(self) -> None:
        """Run the bot."""
        if not self.config.discord_token:
            logger.critical(
                "CRITICAL: DISCORD_BOT_TOKEN is not set in the environment or .env file."
            )
            sys.exit(1)
        
        try:
            await self.client.start(self.config.discord_token)
        except discord.LoginFailure:
            logger.critical("CRITICAL: Failed to log in. Check the DISCORD_BOT_TOKEN.")
            sys.exit(1)
        except Exception as e:
            logger.critical(f"CRITICAL: Bot failed to run. Error: {e}", exc_info=True)
            sys.exit(1)
    
    def run_sync(self) -> None:
        """Run the bot synchronously (for compatibility)."""
        try:
            self.client.run(self.config.discord_token)
        except discord.LoginFailure:
            logger.critical("CRITICAL: Failed to log in. Check the DISCORD_BOT_TOKEN.")
            sys.exit(1)
        except Exception as e:
            logger.critical(f"CRITICAL: Bot failed to run. Error: {e}", exc_info=True)
            sys.exit(1)
