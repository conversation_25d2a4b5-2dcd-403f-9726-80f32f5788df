{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "\n", "A module that was compiled using NumPy 1.x cannot be run in\n", "NumPy 2.0.0rc2 as it may crash. To support both 1.x and 2.x\n", "versions of NumPy, modules must be compiled with NumPy 2.0.\n", "Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n", "\n", "If you are a user of the module, the easiest solution will be to\n", "downgrade to 'numpy<2' or try to upgrade the affected module.\n", "We expect that some modules will need time to support NumPy 2.\n", "\n", "Traceback (most recent call last):  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\traitlets\\config\\application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\tornado\\platform\\asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.1008.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_events.py\", line 641, in run_forever\n", "    self._run_once()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.1008.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_events.py\", line 1987, in _run_once\n", "    handle._run()\n", "  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.1008.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\events.py\", line 88, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\ipykernel\\zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\IPython\\core\\interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\IPython\\core\\interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\IPython\\core\\async_helpers.py\", line 129, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\IPython\\core\\interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\IPython\\core\\interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\IPython\\core\\interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18776\\505069186.py\", line 1, in <module>\n", "    from llama_index.llms.ollama import Ollama\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\llms\\ollama\\__init__.py\", line 1, in <module>\n", "    from llama_index.llms.ollama.base import Ollama\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\llms\\ollama\\base.py\", line 6, in <module>\n", "    from llama_index.core.base.llms.types import (\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\__init__.py\", line 19, in <module>\n", "    from llama_index.core.indices import (\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\indices\\__init__.py\", line 4, in <module>\n", "    from llama_index.core.indices.composability.graph import ComposableGraph\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\indices\\composability\\__init__.py\", line 4, in <module>\n", "    from llama_index.core.indices.composability.graph import ComposableGraph\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\indices\\composability\\graph.py\", line 7, in <module>\n", "    from llama_index.core.indices.base import BaseIndex\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\indices\\base.py\", line 12, in <module>\n", "    from llama_index.core.ingestion import run_transformations\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\ingestion\\__init__.py\", line 2, in <module>\n", "    from llama_index.core.ingestion.pipeline import (\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\ingestion\\pipeline.py\", line 31, in <module>\n", "    from llama_index.core.ingestion.api_utils import get_client\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\ingestion\\api_utils.py\", line 23, in <module>\n", "    from llama_index.core.ingestion.transformations import (\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\ingestion\\transformations.py\", line 272, in <module>\n", "    ConfigurableTransformations = build_configurable_transformation_enum()\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\core\\ingestion\\transformations.py\", line 252, in build_configurable_transformation_enum\n", "    from llama_index.embeddings.huggingface import (\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\embeddings\\huggingface\\__init__.py\", line 1, in <module>\n", "    from llama_index.embeddings.huggingface.base import (\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\llama_index\\embeddings\\huggingface\\base.py\", line 27, in <module>\n", "    from sentence_transformers import SentenceTransformer\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\sentence_transformers\\__init__.py\", line 3, in <module>\n", "    from .datasets import SentencesDataset, ParallelSentencesDataset\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\sentence_transformers\\datasets\\__init__.py\", line 1, in <module>\n", "    from .DenoisingAutoEncoderDataset import DenoisingAutoEncoderDataset\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\sentence_transformers\\datasets\\DenoisingAutoEncoderDataset.py\", line 1, in <module>\n", "    from torch.utils.data import Dataset\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\torch\\__init__.py\", line 1471, in <module>\n", "    from .functional import *  # noqa: F403\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\torch\\functional.py\", line 9, in <module>\n", "    import torch.nn.functional as F\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\torch\\nn\\__init__.py\", line 1, in <module>\n", "    from .modules import *  # noqa: F403\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\torch\\nn\\modules\\__init__.py\", line 35, in <module>\n", "    from .transformer import TransformerEncoder, TransformerDecoder, \\\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\torch\\nn\\modules\\transformer.py\", line 20, in <module>\n", "    device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),\n", "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\torch\\nn\\modules\\transformer.py:20: UserWarning: Failed to initialize NumPy: _ARRAY_API not found (Triggered internally at ..\\torch\\csrc\\utils\\tensor_numpy.cpp:84.)\n", "  device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),\n"]}], "source": ["from llama_index.llms.ollama import Ollama\n", "from llama_index.core import Settings\n", "\n", "Settings.llm = Ollama(model=\"llama3\", request_timeout=60.0)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading files: 100%|██████████| 1/1 [00:00<00:00, 68.64file/s]\n", "Parsing nodes: 100%|██████████| 1/1 [00:40<00:00, 40.81s/it]\n", "Generating embeddings: 100%|██████████| 2048/2048 [01:32<00:00, 22.12it/s]\n", "Generating embeddings: 100%|██████████| 2048/2048 [01:31<00:00, 22.42it/s]\n", "Generating embeddings: 100%|██████████| 1247/1247 [00:56<00:00, 22.26it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Based on the provided context information, I can help you count the number of messages for each user.\n", "\n", "After analyzing the log data, I found that there are three users:\n", "\n", "1. `saolapoggers` with a total of **2** messages.\n", "2. `magma._.` with a total of **1** message.\n", "3. `l_om` with a total of **1** message (no additional information is provided about the user's messages).\n", "\n", "Please note that these counts only consider the messages present in the provided log data and do not account for any potential missing or future messages from these users.\n"]}], "source": ["from llama_index.core import SimpleDirectoryReader, StorageContext, load_index_from_storage\n", "from llama_index.core import VectorStoreIndex\n", "from llama_index.embeddings.ollama import OllamaEmbedding\n", "import os\n", "\n", "from llama_index.core.schema import BaseNode\n", "from llama_index.core.node_parser.interface import NodeParser\n", "import json\n", "from typing import Any, List, Sequence\n", "\n", "class CustomJSONNodeParser(NodeParser):\n", "     def _parse_nodes(\n", "        self, nodes: Sequence[BaseNode], show_progress: bool = False, **kwargs: Any\n", "    ) -> List[BaseNode]:\n", "        # Assuming document is a JSON string\n", "        data = json.loads(nodes[0])\n", "        nodes = []\n", "\n", "        # Extract the \"main\" object and remove the \"messages\" array\n", "        main_data = data.copy()\n", "        main_data.pop(\"messages\", None)\n", "        nodes.append(BaseNode(content=main_data))\n", "\n", "        # Extract messages from the \"messages\" array\n", "        for message in data.get(\"messages\", []):\n", "            node = BaseNode(content=message)\n", "            nodes.append(node)\n", "\n", "        return nodes\n", "\n", "\n", "Settings.embed_model = OllamaEmbedding(model_name=\"nomic-embed-text\")\n", "\n", "PERSIST_DIR = \"./storage\"\n", "if not os.path.exists(PERSIST_DIR):\n", "    # load the documents and create the index\n", "    documents = SimpleDirectoryReader(\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\a\").load_data(show_progress=True)\n", "    parser = CustomJSONNodeParser()\n", "\n", "    index = VectorStoreIndex.from_documents(documents, parser=parser, show_progress=True)\n", "    # store it for later\n", "    index.storage_context.persist(persist_dir=PERSIST_DIR)\n", "else:\n", "    # load the existing index\n", "    storage_context = StorageContext.from_defaults(persist_dir=PERSIST_DIR)\n", "    index = load_index_from_storage(storage_context)\n", "\n", "query_engine = index.as_query_engine()\n", "response = query_engine.query(\n", "    \"For each user in the provided logs, count each user's messages in total\"\n", ")\n", "\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}