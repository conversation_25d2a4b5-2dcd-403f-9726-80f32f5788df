from llama_index.llms.ollama import Ollama
from llama_index.core import Settings
from discord_reader import Discord<PERSON><PERSON>er
from llama_index.core import VectorStoreIndex
from llama_index.embeddings.ollama import OllamaEmbedding
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core import StorageContext, load_index_from_storage
import os

# import llama_index.core
# llama_index.core.set_global_handler("simple")


discord_token = (
    "MTIxNjUyNzM4NzE5NDI5ODM3OA.GUwdIO.qWipHVRzIRXyf8PYK2VS3luzz18x-aTQb7ZyHI"
)

channel_ids = [897883814460801034]

Settings.context_window = 8192
Settings.llm = Ollama(model="llama3", request_timeout=600.0, context_window=8192)
Settings.embed_model = OllamaEmbedding(model_name="nomic-embed-text")

PERSIST_DIR = "./storage"
if not os.path.exists(PERSIST_DIR):
    reader = DiscordReader(discord_token=discord_token)
    documents = reader.load_data(
        channel_ids=channel_ids, limit=20000, oldest_first=False
    )

    index = VectorStoreIndex.from_documents(documents, show_progress=True)
    index.storage_context.persist(persist_dir=PERSIST_DIR)
else:
    storage_context = StorageContext.from_defaults(persist_dir=PERSIST_DIR)
    index = load_index_from_storage(storage_context)

retriever = VectorIndexRetriever(
    index=index,  # type: ignore
    similarity_top_k=200,
)

query_engine = RetrieverQueryEngine(retriever=retriever)

response = query_engine.query(
    'From the discussion history, summarize everything you can about the user "saola"'
)
print(response)
