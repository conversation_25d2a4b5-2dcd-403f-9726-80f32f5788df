"""
LLM-related models for the John Discord bot.

This module contains Pydantic models for LLM responses,
actions, and other AI-related data structures.
"""

from enum import Enum
from typing import Any
from pydantic import BaseModel, Field, field_validator


class LLMAction(str, Enum):
    """Enumeration of possible LLM actions."""
    REPLY = "reply"
    IGNORE = "ignore"
    REACT = "react"


class LLMResponse(BaseModel):
    """Model for LLM response data."""
    
    action: LLMAction = Field(
        ..., 
        description="The action the LLM wants to take"
    )
    message: str | None = Field(
        default=None,
        description="Message content for reply action"
    )
    reason: str = Field(
        ...,
        description="Reason for the chosen action"
    )
    reply_to: str | None = Field(
        default=None,
        description="Message ID to reply to"
    )
    new_facts: list[str] | None = Field(
        default=None,
        description="New facts to store in memory"
    )
    react_to_message_id: str | None = Field(
        default=None,
        description="Message ID to react to"
    )
    reaction_emoji: str | None = Field(
        default=None,
        description="Emoji to use for reaction"
    )
    
    @field_validator("message")
    @classmethod
    def validate_message_for_reply(cls, v: str | None, info: Any) -> str | None:
        """Validate that message exists when action is reply."""
        if hasattr(info, 'data') and info.data.get("action") == LLMAction.REPLY:
            if not v or not v.strip():
                raise ValueError("Message cannot be empty when action is 'reply'")
        return v
    
    @field_validator("react_to_message_id")
    @classmethod
    def validate_reaction_fields(cls, v: str | None, info: Any) -> str | None:
        """Validate reaction fields when action is react."""
        if hasattr(info, 'data') and info.data.get("action") == LLMAction.REACT:
            if not v:
                raise ValueError("react_to_message_id required when action is 'react'")
        return v
    
    @field_validator("reaction_emoji")
    @classmethod
    def validate_reaction_emoji(cls, v: str | None, info: Any) -> str | None:
        """Validate reaction emoji when action is react."""
        if hasattr(info, 'data') and info.data.get("action") == LLMAction.REACT:
            if not v:
                raise ValueError("reaction_emoji required when action is 'react'")
        return v


class MemorySnapshot(BaseModel):
    """Model for memory snapshot data."""
    
    facts: list[str] = Field(
        default_factory=list,
        description="List of stored facts"
    )
    
    def add_facts(self, new_facts: list[str]) -> list[str]:
        """Add new facts, returning only the ones that were actually added."""
        if not new_facts:
            return []
        
        # Convert existing facts to lowercase for comparison
        existing_lower = {fact.strip().lower() for fact in self.facts}
        added_facts = []
        
        for fact in new_facts:
            if isinstance(fact, str):
                fact_clean = fact.strip()
                if fact_clean and fact_clean.lower() not in existing_lower:
                    self.facts.append(fact_clean)
                    existing_lower.add(fact_clean.lower())
                    added_facts.append(fact_clean)
        
        return added_facts


class LLMContext(BaseModel):
    """Model for LLM context data."""
    
    trigger_type: str = Field(
        ...,
        description="Type of trigger that initiated the LLM call"
    )
    channel_name: str = Field(
        ...,
        description="Name of the Discord channel"
    )
    is_react_only: bool = Field(
        default=False,
        description="Whether this is a react-only channel"
    )
    available_emojis: list[str] = Field(
        default_factory=list,
        description="List of available custom emojis"
    )
    memory: MemorySnapshot = Field(
        default_factory=MemorySnapshot,
        description="Current memory snapshot"
    )
    messages: list[dict[str, Any]] = Field(
        default_factory=list,
        description="Recent message history"
    )
    current_time: str = Field(
        ...,
        description="Current UTC time"
    )
    local_time: str = Field(
        ...,
        description="Current local time"
    )
    local_timezone_name: str = Field(
        ...,
        description="Local timezone name"
    )
