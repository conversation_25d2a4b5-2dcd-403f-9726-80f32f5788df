# John Discord <PERSON><PERSON> is an LLM-powered Discord bot with personality and memory. He can participate in conversations naturally, remember facts about users, and adapt his behavior based on channel settings.

## Features

- **LLM Integration**: Uses Google Gemini 2.5 Flash for natural conversation
- **Memory System**: Persistent storage of facts about users and conversations
- **React-Only Channels**: Special mode where bot can only react, not reply
- **Typing Simulation**: Realistic typing delays based on message length
- **Inactivity Monitoring**: Periodic checks for conversation engagement
- **Modular Architecture**: Clean separation of concerns with type safety

## Architecture

The bot is built with a modular architecture:

```
src/john/
├── core/           # Main bot class and event handlers
├── services/       # Business logic services
├── models/         # Pydantic data models
├── config/         # Configuration and prompt templates
└── utils/          # Utility functions
```

### Core Components

- **JohnBot**: Main bot class that coordinates all functionality
- **BotService**: Orchestrates LLM interactions and Discord actions
- **LLMService**: Handles all LLM communication and prompt building
- **MemoryService**: Manages persistent fact storage
- **CacheService**: In-memory message caching for context

### Key Features

#### React-Only Channels
Some channels are configured as "react-only" where <PERSON> can only react with emojis but cannot send text replies. This allows for more controlled interaction in busy channels.

#### Memory System
John remembers facts about users and conversations, storing them persistently. He can learn new information and recall it in future conversations.

#### Typing Simulation
John simulates realistic typing delays based on message length and configured typing speed, making interactions feel more natural.

## Configuration

Configuration is handled through environment variables and Pydantic models:

### Required Environment Variables

- `DISCORD_BOT_TOKEN`: Your Discord bot token
- `GOOGLE_AI_API_KEY` or `GOOGLE_API_KEY`: Google AI API key for LLM

### Optional Configuration

Most configuration is hardcoded in `src/john/models/config_models.py` but can be customized:

- Allowed servers and channels
- React-only channel list
- LLM model settings
- Typing simulation parameters
- Memory and cache settings

## Installation

1. Install dependencies:
```bash
pip install -e .
```

2. Set up environment variables:
```bash
export DISCORD_BOT_TOKEN="your_discord_token"
export GOOGLE_AI_API_KEY="your_google_api_key"
```

3. Run the bot:
```bash
python -m src.john.main
```

## Development

### Running Tests

```bash
pytest tests/
```

### Code Quality

The project uses modern Python tooling:

- **Black**: Code formatting
- **Ruff**: Linting and import sorting
- **MyPy**: Type checking
- **Pytest**: Testing framework

Run quality checks:

```bash
black src/ tests/
ruff check src/ tests/
mypy src/
```

### Project Structure

```
john/
├── src/john/                 # Main package
│   ├── core/                # Bot core and events
│   ├── services/            # Business logic
│   ├── models/              # Data models
│   ├── config/              # Configuration
│   └── utils/               # Utilities
├── tests/                   # Test suite
├── debug/                   # Debug output (auto-generated)
├── old/                     # Legacy code (preserved)
├── pyproject.toml          # Project configuration
└── README.md               # This file
```

## Usage

### Slash Commands

- `/ping`: Check if John is alive
- `/llm`: Manually trigger LLM processing on channel history

### Behavior

John will automatically:
- Process new messages in allowed channels
- React or reply based on channel configuration
- Remember facts about users and conversations
- Check for inactivity and engage when appropriate
- Simulate realistic typing delays

### Channel Types

- **Normal Channels**: John can both reply and react
- **React-Only Channels**: John can only react with emojis

## Customization

### Adding New Prompts

Prompts are modular and defined in `src/john/config/prompts.py`. You can:
- Modify existing prompt components
- Add new behavioral guidelines
- Customize output formats

### Extending Services

The service layer is designed for extension:
- Add new services in `src/john/services/`
- Implement new data models in `src/john/models/`
- Add utility functions in `src/john/utils/`

### Configuration Changes

Update configuration in `src/john/models/config_models.py`:
- Modify allowed servers/channels
- Adjust LLM parameters
- Change behavioral settings

## Contributing

1. Follow the existing code style and architecture
2. Add tests for new functionality
3. Update documentation as needed
4. Use type hints throughout
5. Follow the modular design patterns

## License

This project is for personal use. Please respect Discord's Terms of Service and API guidelines when using this bot.
