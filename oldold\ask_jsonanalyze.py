import os

import logging
import sys

logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logging.getLogger().addHandler(logging.StreamHandler(stream=sys.stdout))

import json

# specify the folder path
folder_path = "C:\\Users\\<USER>\\Downloads\\a"

# initialize an empty array to store the JSON objects
json_objects = []

# iterate over each file in the folder
for filename in os.listdir(folder_path):
    # check if the file has a .json extension
    if filename.endswith(".json"):
        file_path = os.path.join(folder_path, filename)

        # open the file and load its JSON content
        with open(file_path, "r", encoding='utf8') as file:
            json_data = json.load(file)

            # append the JSON object to the array
            json_objects.append(json_data["messages"])


from llama_index.core.query_engine import JSONalyzeQueryEngine
from llama_index.llms.ollama import Ollama

llm = <PERSON>llama(model="llama3", request_timeout=600.0)

json_stats_query_engine = JSONalyzeQueryEngine(
    list_of_dict=json_objects[0],
    llm=llm,
    verbose=True,
)

response = json_stats_query_engine.query('From the discussion history, summarize everything you can about the user "saola"')

print(response)