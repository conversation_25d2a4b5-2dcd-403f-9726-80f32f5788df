"""
Cache service for the John Discord bot.

This service handles in-memory caching of Discord messages
for efficient context retrieval and management.
"""

import asyncio
import logging
import time
from collections import deque
from typing import Any

import discord

from ..models.message_models import MessageDict
from ..utils.discord_utils import message_to_dict, clean_message_content
from ..config.settings import get_config

logger = logging.getLogger(__name__)


class CacheService:
    """Service for managing in-memory message cache."""
    
    def __init__(self) -> None:
        """Initialize the cache service."""
        self._cache: dict[int, deque[MessageDict]] = {}
        self._lock = asyncio.Lock()
        self._last_message_timestamps: dict[int, float] = {}
    
    async def get_history(
        self, 
        channel_id: int, 
        start_message_id: int | None = None
    ) -> list[MessageDict]:
        """
        Get the message history for a channel.
        
        Args:
            channel_id: Discord channel ID
            start_message_id: Optional message ID to start from (unused for now)
            
        Returns:
            list[MessageDict]: List of cached messages
        """
        async with self._lock:
            cache_deque = self._cache.get(channel_id)
            if not cache_deque:
                return []
            
            history_list = list(cache_deque)
        
        logger.debug(
            f"Returning cached history ({len(history_list)} messages) "
            f"for channel {channel_id}"
        )
        return history_list
    
    async def add_message(self, channel_id: int, message_dict: MessageDict) -> None:
        """
        Add a message to the cache for a given channel.
        
        Args:
            channel_id: Discord channel ID
            message_dict: Message data to cache
        """
        async with self._lock:
            if channel_id not in self._cache:
                self._cache[channel_id] = deque()
                self._last_message_timestamps[channel_id] = 0.0
        
        # Append outside the main lock, as deque append is thread-safe
        self._cache[channel_id].append(message_dict)
        
        # Update timestamp
        async with self._lock:
            self._last_message_timestamps[channel_id] = time.time()
        
        logger.debug(
            f"Added message {message_dict.id} to cache for channel {channel_id}"
        )
    
    async def populate_initial_cache(
        self,
        channel: discord.TextChannel,
        fetch_limit: int,
        start_message_id: int | None = None,
    ) -> None:
        """
        Fetch initial history for a channel and populate the cache.
        
        Args:
            channel: Discord text channel
            fetch_limit: Maximum number of messages to fetch
            start_message_id: Optional message ID to start after
        """
        logger.info(
            f"Performing initial cache fetch for #{channel.name} "
            f"(limit: {fetch_limit}, after_id: {start_message_id})"
        )
        
        guild = channel.guild
        if not guild:
            logger.error(
                f"Cannot fetch history for channel {channel.id}: No guild context"
            )
            return
        
        messages_list: list[MessageDict] = []
        
        # Prepare the after object for Discord API
        after_obj = None
        if start_message_id:
            try:
                after_obj = discord.Object(id=start_message_id)
            except (ValueError, TypeError):
                logger.error(
                    f"Invalid start_message_id {start_message_id}. "
                    "Fetching without cutoff."
                )
        
        # Fetch messages from Discord
        async for message in channel.history(
            limit=fetch_limit, 
            after=after_obj, 
            oldest_first=False
        ):
            if message.author and message.content:
                processed = await self._process_initial_message(message, guild)
                if processed:
                    messages_list.append(processed)
        
        # Reverse to get chronological order (oldest first)
        messages_list.reverse()
        
        # Store in cache
        async with self._lock:
            self._cache[channel.id] = deque(messages_list)
        
        logger.info(
            f"Populated initial cache for #{channel.name} "
            f"with {len(messages_list)} messages"
        )
    
    async def _process_initial_message(
        self,
        message: discord.Message,
        guild: discord.Guild,
    ) -> MessageDict | None:
        """
        Convert and clean a raw Discord message for initial cache population.
        
        Args:
            message: Discord message object
            guild: Discord guild object
            
        Returns:
            MessageDict | None: Processed message or None if invalid
        """
        try:
            msg_dict_raw = await message_to_dict(message, guild)
            content = msg_dict_raw.get("content", "")
            cleaned = clean_message_content(content)
            
            if not cleaned:
                return None
            
            # Update content with cleaned version
            msg_dict_raw["content"] = cleaned
            
            # Convert to MessageDict model
            return MessageDict.from_dict(msg_dict_raw)
            
        except Exception as e:
            logger.error(f"Error processing message {message.id}: {e}")
            return None
    
    async def delete_message(self, channel_id: int, message_id: int) -> bool:
        """
        Remove a message from the cache by its ID.
        
        Args:
            channel_id: Discord channel ID
            message_id: Discord message ID
            
        Returns:
            bool: True if message was found and removed, False otherwise
        """
        message_id_str = str(message_id)
        
        async with self._lock:
            cache_deque = self._cache.get(channel_id)
            if not cache_deque:
                logger.debug(
                    f"Delete request for message {message_id_str} ignored: "
                    f"Channel {channel_id} not cached"
                )
                return False
            
            # Find and remove the message
            for i, msg_dict in enumerate(cache_deque):
                if msg_dict.id == message_id_str:
                    del cache_deque[i]
                    logger.info(
                        f"Removed deleted message {message_id_str} "
                        f"from cache for channel {channel_id}"
                    )
                    return True
            
            logger.debug(
                f"Delete request for message {message_id_str} ignored: "
                f"Message not found in cache for channel {channel_id}"
            )
            return False
    
    async def update_message(
        self, 
        channel_id: int, 
        updated_message: discord.Message
    ) -> bool:
        """
        Update an existing message in the cache with edited content.
        
        Args:
            channel_id: Discord channel ID
            updated_message: Updated Discord message object
            
        Returns:
            bool: True if message was found and updated, False otherwise
        """
        message_id_str = str(updated_message.id)
        
        if not updated_message.guild:
            logger.warning(
                f"Cannot update message {message_id_str}: No guild context"
            )
            return False
        
        # Process the updated message
        try:
            cleaned_content = clean_message_content(updated_message.content)
            if not cleaned_content:
                logger.debug(
                    f"Skipping update for message {message_id_str}: "
                    "Content empty after cleaning"
                )
                return False
            
            updated_msg_dict_raw = await message_to_dict(
                updated_message, 
                updated_message.guild
            )
            updated_msg_dict_raw["content"] = cleaned_content
            updated_msg_dict = MessageDict.from_dict(updated_msg_dict_raw)
            
        except Exception as e:
            logger.error(
                f"Error converting updated message {message_id_str} to dict: {e}"
            )
            return False
        
        # Update in cache
        async with self._lock:
            cache_deque = self._cache.get(channel_id)
            if not cache_deque:
                logger.debug(
                    f"Update request for message {message_id_str} ignored: "
                    f"Channel {channel_id} not cached"
                )
                return False
            
            # Find and update the message
            for i, msg_dict in enumerate(cache_deque):
                if msg_dict.id == message_id_str:
                    cache_deque[i] = updated_msg_dict
                    logger.info(
                        f"Updated edited message {message_id_str} "
                        f"in cache for channel {channel_id}"
                    )
                    return True
            
            logger.debug(
                f"Update request for message {message_id_str} ignored: "
                f"Message not found in cache for channel {channel_id}"
            )
            return False
    
    async def get_last_message_timestamp(self, channel_id: int) -> float | None:
        """
        Get the timestamp of the last message added to the cache.
        
        Args:
            channel_id: Discord channel ID
            
        Returns:
            float | None: Timestamp or None if no messages
        """
        async with self._lock:
            return self._last_message_timestamps.get(channel_id)
    
    async def get_cache_stats(self) -> dict[str, Any]:
        """
        Get statistics about the current cache state.
        
        Returns:
            dict[str, Any]: Cache statistics
        """
        async with self._lock:
            stats = {
                "total_channels": len(self._cache),
                "total_messages": sum(len(deque_) for deque_ in self._cache.values()),
                "channels": {
                    channel_id: len(deque_)
                    for channel_id, deque_ in self._cache.items()
                }
            }
        
        return stats
