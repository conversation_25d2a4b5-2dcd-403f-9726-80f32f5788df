"""
Main entry point for the John Discord bot.

This module sets up logging and starts the bot.
"""

import logging
import sys
from pathlib import Path

from .core.bot import <PERSON><PERSON><PERSON>


def setup_logging() -> None:
    """Set up logging configuration for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s:%(levelname)s:%(name)s: %(message)s",
        force=True,
    )
    
    # Get the application's logger
    logger = logging.getLogger(__name__)
    logger.info("John Discord Bot starting up...")


def main() -> None:
    """Main entry point for the bot."""
    setup_logging()
    
    try:
        bot = JohnBot()
        bot.run_sync()
    except KeyboardInterrupt:
        logging.info("Bot shutdown requested by user")
        sys.exit(0)
    except Exception as e:
        logging.critical(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
