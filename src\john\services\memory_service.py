"""
Memory service for the John Discord bot.

This service handles persistent storage and retrieval of facts
and memories about users and conversations.
"""

import json
import asyncio
import logging
from pathlib import Path
from typing import Any
from functools import partial

from ..models.llm_models import MemorySnapshot
from ..config.settings import get_config

logger = logging.getLogger(__name__)


class MemoryService:
    """Service for managing bot memory and persistent facts."""
    
    def __init__(self, memory_path: str | None = None) -> None:
        """
        Initialize the memory service.
        
        Args:
            memory_path: Path to the memory file. If None, uses config default.
        """
        config = get_config()
        self.memory_path = Path(memory_path or config.memory_path)
        self._memory = MemorySnapshot()
        self._lock = asyncio.Lock()
    
    async def load_memory(self) -> None:
        """Load memory from the JSON file asynchronously."""
        loop = asyncio.get_running_loop()
        
        if self.memory_path.exists():
            try:
                def _load_file() -> dict[str, Any]:
                    with open(self.memory_path, "r", encoding="utf-8") as f:
                        return json.load(f)
                
                data = await loop.run_in_executor(None, _load_file)
                
                async with self._lock:
                    self._memory = MemorySnapshot(
                        facts=data.get("facts", [])
                    )
                
                logger.info(
                    f"Memory loaded successfully from {self.memory_path} "
                    f"({len(self._memory.facts)} facts)"
                )
                
            except Exception as e:
                logger.error(
                    f"Failed to load memory from {self.memory_path}: {e}. "
                    "Using default empty memory."
                )
                async with self._lock:
                    self._memory = MemorySnapshot()
        else:
            logger.info(
                f"Memory file {self.memory_path} not found. "
                "Starting with empty memory."
            )
            async with self._lock:
                self._memory = MemorySnapshot()
    
    async def save_memory(self) -> None:
        """Save the current memory state to the JSON file asynchronously."""
        loop = asyncio.get_running_loop()
        
        try:
            async with self._lock:
                memory_data = self._memory.model_dump()
            
            def _save_file() -> None:
                # Ensure parent directory exists
                self.memory_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(self.memory_path, "w", encoding="utf-8") as f:
                    json.dump(memory_data, f, indent=2, ensure_ascii=False)
            
            await loop.run_in_executor(None, _save_file)
            logger.debug(f"Memory saved successfully to {self.memory_path}")
            
        except Exception as e:
            logger.error(f"Failed to save memory to {self.memory_path}: {e}")
    
    async def get_memory_snapshot(self) -> MemorySnapshot:
        """
        Get a copy of the current memory state.
        
        Returns:
            MemorySnapshot: Copy of current memory
        """
        async with self._lock:
            return MemorySnapshot(facts=self._memory.facts.copy())
    
    async def update_memory(self, proposed_facts: list[str]) -> bool:
        """
        Add unique new facts to memory and save if changes were made.
        
        Args:
            proposed_facts: List of new facts to potentially add
            
        Returns:
            bool: True if any facts were added, False otherwise
        """
        if not isinstance(proposed_facts, list) or not proposed_facts:
            return False
        
        logger.info(
            f"Attempting to update memory with {len(proposed_facts)} proposed facts"
        )
        
        async with self._lock:
            added_facts = self._memory.add_facts(proposed_facts)
            updated = len(added_facts) > 0
        
        if updated:
            logger.info(
                f"Added {len(added_facts)} new facts. "
                f"Skipped {len(proposed_facts) - len(added_facts)}."
            )
            await self.save_memory()
        else:
            logger.info(
                f"No new facts to add. Skipped {len(proposed_facts)}."
            )
        
        return updated
    
    async def get_facts_count(self) -> int:
        """
        Get the current number of stored facts.
        
        Returns:
            int: Number of facts in memory
        """
        async with self._lock:
            return len(self._memory.facts)
    
    async def search_facts(self, query: str) -> list[str]:
        """
        Search for facts containing the query string.
        
        Args:
            query: Search query string
            
        Returns:
            list[str]: List of matching facts
        """
        if not query.strip():
            return []
        
        query_lower = query.strip().lower()
        
        async with self._lock:
            matching_facts = [
                fact for fact in self._memory.facts
                if query_lower in fact.lower()
            ]
        
        return matching_facts
    
    async def clear_memory(self) -> None:
        """Clear all stored facts (use with caution)."""
        logger.warning("Clearing all memory facts")
        
        async with self._lock:
            self._memory = MemorySnapshot()
        
        await self.save_memory()
        logger.info("Memory cleared successfully")
